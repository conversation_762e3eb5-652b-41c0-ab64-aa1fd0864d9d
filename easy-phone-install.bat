@echo off
echo.
echo ================================================================
echo              📱 BYBIT TRADING BOT - EASY PHONE INSTALLER
echo ================================================================
echo.
echo 🎯 This script will build and prepare your trading bot for
echo    easy installation on your Motorola G32 phone.
echo.
echo 🚀 Choose your installation method:
echo.
echo [1] PWA (Progressive Web App) - RECOMMENDED
echo     - Instant installation from browser
echo     - No APK needed
echo     - Works offline
echo     - Push notifications
echo.
echo [2] Native Android APK
echo     - Full native app
echo     - Optimized for Motorola G32
echo     - Complete device integration
echo.
echo [3] Mobile Web Interface
echo     - Browser-based access
echo     - No installation required
echo     - Works on any device
echo.
set /p choice="Choose option (1-3): "

if "%choice%"=="1" goto pwa_install
if "%choice%"=="2" goto apk_build
if "%choice%"=="3" goto web_access
goto invalid_choice

:pwa_install
echo.
echo 🚀 PROGRESSIVE WEB APP INSTALLATION
echo ====================================
echo.
echo ✅ The PWA is ready! Here's how to install on your phone:
echo.
echo 📱 ANDROID PHONE:
echo    1. Open Chrome browser
echo    2. Go to: http://*************:8000
echo    3. Look for "Install" banner at bottom
echo    4. Tap "Install" or Menu ⋮ > "Add to Home screen"
echo    5. Confirm installation
echo.
echo 📱 IPHONE:
echo    1. Open Safari browser  
echo    2. Go to: http://*************:8000
echo    3. Tap Share button □↗
echo    4. Tap "Add to Home Screen"
echo    5. Tap "Add"
echo.
echo 🎯 QUICK ACCESS:
echo    Open mobile-install.html for QR code and detailed instructions
echo.
start mobile-install.html
echo ✅ Installation guide opened in browser
goto end

:apk_build
echo.
echo 📦 NATIVE ANDROID APK BUILD
echo ============================
echo.
echo 🔧 Building APK for your Motorola G32...
echo.

REM Check if we're in the right directory
if not exist "mobile" (
    echo ❌ Error: mobile directory not found
    echo    Make sure you're running this from the BOT directory
    pause
    exit /b 1
)

cd mobile

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js not found. Please install Node.js first.
    echo    Download from: https://nodejs.org/
    pause
    exit /b 1
)

REM Install dependencies if needed
if not exist "node_modules" (
    echo 📦 Installing dependencies...
    call npm install
)

REM Build the APK
echo 🏗️  Building APK...
if exist "build-moto-g32.ps1" (
    powershell -ExecutionPolicy Bypass -File build-moto-g32.ps1
) else (
    echo 📱 Running Android build...
    call npm run build:android
)

echo.
echo ✅ APK BUILD COMPLETE!
echo.
echo 📲 TO INSTALL ON YOUR MOTOROLA G32:
echo.
echo 1️⃣  ENABLE INSTALLATION:
echo    - Settings > Security > Install unknown apps
echo    - Enable for your file manager
echo.
echo 2️⃣  TRANSFER APK:
echo    - Copy APK to phone via USB/Bluetooth/Cloud
echo    - APK location: android\app\build\outputs\apk\release\
echo.
echo 3️⃣  INSTALL:
echo    - Open APK file on phone
echo    - Tap "Install"
echo    - Grant permissions
echo.
cd ..
goto end

:web_access
echo.
echo 🌐 MOBILE WEB INTERFACE
echo =======================
echo.
echo ✅ Your trading bot is accessible via web browser!
echo.
echo 📱 ACCESS ON YOUR PHONE:
echo    1. Open any browser (Chrome, Safari, Firefox)
echo    2. Go to: http://*************:8000
echo    3. Bookmark for quick access
echo.
echo 🎯 FEATURES:
echo    ✓ Full trading dashboard
echo    ✓ Real-time updates
echo    ✓ Mobile-optimized interface
echo    ✓ Touch-friendly controls
echo.
echo 💡 TIP: For best experience, use PWA installation (option 1)
echo.
start http://*************:8000
echo ✅ Opening trading bot in browser...
goto end

:invalid_choice
echo.
echo ❌ Invalid choice. Please run the script again and choose 1, 2, or 3.
pause
exit /b 1

:end
echo.
echo ================================================================
echo              🎉 INSTALLATION READY!
echo ================================================================
echo.
echo 🔗 QUICK LINKS:
echo    📱 PWA Installation: mobile-install.html
echo    🌐 Web Interface: http://*************:8000
echo    📚 Documentation: mobile/README.md
echo.
echo 💡 SUPPORT:
echo    - Check mobile/ANDROID_SETUP.md for detailed instructions
echo    - Ensure your phone is on the same WiFi network
echo    - For issues, check firewall settings
echo.
echo 🚀 Enjoy autonomous trading on your phone!
echo.
pause
