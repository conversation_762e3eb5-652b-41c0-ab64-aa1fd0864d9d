<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Offline - Bybit Trading Bot</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
            color: #ffffff;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .offline-container {
            text-align: center;
            max-width: 400px;
            padding: 40px 20px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .offline-icon {
            font-size: 64px;
            margin-bottom: 20px;
            opacity: 0.7;
        }

        .offline-title {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 12px;
            color: #ffffff;
        }

        .offline-subtitle {
            font-size: 16px;
            color: #a0a0a0;
            margin-bottom: 30px;
            line-height: 1.5;
        }

        .retry-button {
            background: linear-gradient(135deg, #00ff88 0%, #00cc6a 100%);
            color: #000000;
            border: none;
            padding: 12px 24px;
            border-radius: 12px;
            font-weight: 600;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 20px;
        }

        .retry-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 255, 136, 0.3);
        }

        .offline-features {
            margin-top: 20px;
            text-align: left;
            background: rgba(0, 0, 0, 0.3);
            padding: 20px;
            border-radius: 12px;
        }

        .feature-item {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
            font-size: 14px;
            color: #c0c0c0;
        }

        .feature-icon {
            margin-right: 8px;
            font-size: 16px;
        }

        .last-sync {
            margin-top: 20px;
            padding: 12px;
            background: rgba(0, 255, 136, 0.1);
            border-radius: 8px;
            font-size: 12px;
            color: #00ff88;
        }
    </style>
</head>
<body>
    <div class="offline-container">
        <div class="offline-icon">📡</div>
        <h1 class="offline-title">You're Offline</h1>
        <p class="offline-subtitle">
            Your trading bot is temporarily disconnected. Don't worry - your trades are still running on the server.
        </p>
        
        <button class="retry-button" onclick="retryConnection()">
            Retry Connection
        </button>

        <div class="offline-features">
            <div class="feature-item">
                <span class="feature-icon">📊</span>
                View cached dashboard data
            </div>
            <div class="feature-item">
                <span class="feature-icon">📈</span>
                Check portfolio performance
            </div>
            <div class="feature-item">
                <span class="feature-icon">⚙️</span>
                Access saved settings
            </div>
            <div class="feature-item">
                <span class="feature-icon">🔔</span>
                Receive notifications when online
            </div>
        </div>

        <div class="last-sync" id="lastSync">
            Last sync: Checking...
        </div>
    </div>

    <script>
        // Check connection status
        function updateConnectionStatus() {
            const lastSyncElement = document.getElementById('lastSync');
            const lastSync = localStorage.getItem('lastSync');
            
            if (lastSync) {
                const syncTime = new Date(parseInt(lastSync));
                lastSyncElement.textContent = `Last sync: ${syncTime.toLocaleString()}`;
            } else {
                lastSyncElement.textContent = 'Last sync: Unknown';
            }
        }

        // Retry connection
        function retryConnection() {
            if (navigator.onLine) {
                window.location.reload();
            } else {
                alert('Still offline. Please check your internet connection.');
            }
        }

        // Update online/offline status
        function updateOnlineStatus() {
            if (navigator.onLine) {
                retryConnection();
            }
        }

        // Listen for connection changes
        window.addEventListener('online', updateOnlineStatus);
        window.addEventListener('offline', () => {
            console.log('Device is offline');
        });

        // Check every 5 seconds if we're back online
        setInterval(() => {
            if (navigator.onLine) {
                fetch('/', { cache: 'no-cache' })
                    .then(() => {
                        window.location.reload();
                    })
                    .catch(() => {
                        // Still offline
                    });
            }
        }, 5000);

        // Initialize
        updateConnectionStatus();
    </script>
</body>
</html>
