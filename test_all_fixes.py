#!/usr/bin/env python3
"""
Test the fixed components before running the full system
"""
import asyncio
import sys

async def test_fixed_components():
    print("🔥 TESTING ALL FIXED COMPONENTS...")
    print("=" * 60)
    
    try:
        # Test 1: Configuration loading
        print("1. Testing Configuration...")
        from bybit_bot.core.config import BotConfig
        config = BotConfig()
        print(f"   ✅ Config type: {type(config.data_crawler)}")
        
        # Test 2: Database with metadata column
        print("2. Testing Database with metadata column...")
        from bybit_bot.database.connection import DatabaseManager
        db_manager = DatabaseManager(config)
        print("   ✅ Database manager created")
        
        # Test 3: SocialSentimentCrawler with dict config
        print("3. Testing SocialSentimentCrawler...")
        from bybit_bot.data_crawler.social_sentiment_crawler import SocialSentimentCrawler
        social_crawler = SocialSentimentCrawler(config, db_manager)
        print(f"   ✅ Social crawler created, keywords: {len(social_crawler.crypto_keywords)}")
        
        # Test 4: MarketDataCrawler with initialize method
        print("4. Testing MarketDataCrawler...")
        from bybit_bot.data_crawler.market_data_crawler import MarketDataCrawler
        market_crawler = MarketDataCrawler(config, db_manager)
        has_initialize = hasattr(market_crawler, 'initialize')
        print(f"   ✅ Market crawler created, has initialize: {has_initialize}")
        
        # Test 5: SelfCorrectingCodeEvolution with flexible params
        print("5. Testing SelfCorrectingCodeEvolution...")
        from bybit_bot.ai.self_correcting_code_evolution import SelfCorrectingCodeEvolution
        code_evolution = SelfCorrectingCodeEvolution(
            config=config,
            database_manager=db_manager,
            base_path="/e/The_real_deal_copy/Bybit_Bot/BOT"
        )
        print("   ✅ Code evolution created with base_path")
        
        # Test 6: MetaCognitionEngine with keyword args
        print("6. Testing MetaCognitionEngine...")
        from bybit_bot.ai.meta_cognition_engine import MetaCognitionEngine
        meta_engine = MetaCognitionEngine(config=config, database_manager=db_manager)
        print("   ✅ Meta cognition engine created")
        
        # Test 7: Database schema check
        print("7. Testing Database schema...")
        import sqlite3
        conn = sqlite3.connect('bybit_trading_bot.db')
        cursor = conn.cursor()
        cursor.execute('PRAGMA table_info(positions)')
        columns = [col[1] for col in cursor.fetchall()]
        has_metadata = 'metadata' in columns
        conn.close()
        print(f"   ✅ Positions table has metadata column: {has_metadata}")
        
        print("\n🎉 ALL COMPONENTS FIXED AND READY!")
        print("🚀 The unified system should now start successfully!")
        return True
        
    except Exception as e:
        print(f"\n❌ ERROR in component test: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(test_fixed_components())
    sys.exit(0 if success else 1)
