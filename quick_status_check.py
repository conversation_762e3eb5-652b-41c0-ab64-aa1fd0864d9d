#!/usr/bin/env python3
"""
Quick Status Check - Get validation results fast
"""
import sys
from pathlib import Path

# Add the BOT directory to Python path
bot_dir = Path(__file__).parent
sys.path.insert(0, str(bot_dir))

def check_status():
    try:
        from bybit_bot.core.config import get_config
        
        print("=" * 60)
        print("🚀 QUICK STATUS CHECK")
        print("=" * 60)
        
        # Load config
        config = get_config()
        
        # Check SuperGPT
        supergpt_enabled = hasattr(config, 'supergpt') and getattr(config.supergpt, 'enabled', False)
        print(f"✅ SuperGPT: {'ENABLED' if supergpt_enabled else 'DISABLED'}")
        
        # Check Autonomous
        autonomous_enabled = hasattr(config, 'autonomous') and getattr(config.autonomous, 'enabled', False)
        print(f"✅ Autonomous: {'ENABLED' if autonomous_enabled else 'DISABLED'}")
        
        # Check Database optimization
        db_optimized = hasattr(config.database, 'pragma') and config.database.pragma
        print(f"✅ Database: {'OPTIMIZED' if db_optimized else 'BASIC'}")
        
        # Calculate readiness
        critical_failures = 0
        if not supergpt_enabled:
            critical_failures += 1
        if not autonomous_enabled:
            critical_failures += 1
            
        readiness = max(0, 100 - (critical_failures * 50))
        
        print("=" * 60)
        print(f"🎯 SYSTEM READINESS: {readiness}%")
        
        if readiness == 100:
            print("🎉 SYSTEM READY FOR MAXIMUM PROFIT GENERATION!")
            print("🚀 ALL FEATURES ACTIVE - LAUNCHING RECOMMENDED")
        else:
            print(f"⚠️ {critical_failures} critical issues remaining")
            
        print("=" * 60)
        return readiness
        
    except Exception as e:
        print(f"❌ Error during status check: {e}")
        return 0

if __name__ == "__main__":
    readiness = check_status()
    sys.exit(0 if readiness == 100 else 1)
