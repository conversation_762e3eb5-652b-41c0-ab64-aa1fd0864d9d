#!/usr/bin/env python3
"""
E-Drive Conda Environment Test Script
Tests that conda environment is properly activated and Python is working
"""

import sys
import os
import platform

def test_environment():
    """Test the conda environment setup"""
    print("=" * 60)
    print("E-DRIVE CONDA ENVIRONMENT TEST")
    print("=" * 60)
    
    # Python version
    print(f"Python Version: {platform.python_version()}")
    print(f"Python Executable: {sys.executable}")
    print(f"Python Path: {sys.path[0]}")
    
    # Environment variables
    print(f"CONDA_PREFIX: {os.environ.get('CONDA_PREFIX', 'NOT SET')}")
    print(f"CONDA_DEFAULT_ENV: {os.environ.get('CONDA_DEFAULT_ENV', 'NOT SET')}")
    
    # Check if we're in the right environment
    if "bybit-trader" in str(sys.executable):
        print("[SUCCESS] Running in bybit-trader environment")
    else:
        print("[WARNING] May not be in bybit-trader environment")
    
    # Check E drive usage
    if sys.executable.startswith("E:"):
        print("[SUCCESS] Using E drive Python installation")
    else:
        print("[WARNING] Not using E drive Python installation")
    
    # Test imports
    try:
        import numpy
        print(f"[OK] NumPy {numpy.__version__}")
    except ImportError:
        print("[ERROR] NumPy not available")
    
    try:
        import pandas
        print(f"[OK] Pandas {pandas.__version__}")
    except ImportError:
        print("[ERROR] Pandas not available")
        
    try:
        import ccxt
        print(f"[OK] CCXT {ccxt.__version__}")
    except ImportError:
        print("[ERROR] CCXT not available")
    
    print("=" * 60)
    print("Test completed!")
    print("=" * 60)

if __name__ == "__main__":
    test_environment()
