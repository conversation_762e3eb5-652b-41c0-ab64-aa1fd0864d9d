const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron');

// Expose protected methods that allow the renderer process to use
// the ipcRenderer without exposing the entire object
contextBridge.exposeInMainWorld('electronAPI', {
  // Trading controls
  startTrading: () => ipcRenderer.invoke('start-trading'),
  stopTrading: () => ipcRenderer.invoke('stop-trading'),
  getTradingStatus: () => ipcRenderer.invoke('get-trading-status'),
  emergencyStop: () => ipcRenderer.invoke('emergency-stop'),

  // Event listeners
  onTradingStatus: (callback) => {
    ipcRenderer.on('trading-status', (event, data) => callback(data));
  },
  onTradingLog: (callback) => {
    ipcRenderer.on('trading-log', (event, data) => callback(data));
  },
  onTradingError: (callback) => {
    ipcRenderer.on('trading-error', (event, data) => callback(data));
  },
  onNavigateTo: (callback) => {
    ipcRenderer.on('navigate-to', (event, route) => callback(route));
  },

  // Remove listeners
  removeAllListeners: (channel) => {
    ipcRenderer.removeAllListeners(channel);
  },

  // System info
  platform: process.platform,
  isElectron: true,

  // Window management
  minimizeToTray: () => ipcRenderer.invoke('minimize-to-tray'),
  showWindow: () => ipcRenderer.invoke('show-window'),

  // Settings
  getSettings: () => ipcRenderer.invoke('get-settings'),
  saveSettings: (settings) => ipcRenderer.invoke('save-settings', settings),

  // File operations
  openLogsFolder: () => ipcRenderer.invoke('open-logs-folder'),
  exportLogs: () => ipcRenderer.invoke('export-logs'),

  // System monitoring
  getSystemStats: () => ipcRenderer.invoke('get-system-stats'),
  onSystemStats: (callback) => {
    ipcRenderer.on('system-stats', (event, data) => callback(data));
  }
});
