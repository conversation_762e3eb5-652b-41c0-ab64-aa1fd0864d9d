const { app, BrowserWindow, Tray, <PERSON>u, ipc<PERSON>ain, dialog, shell, Notification } = require('electron');
const { spawn } = require('child_process');
const path = require('path');
const fs = require('fs');

// Keep a global reference of the window object
let mainWindow;
let tray = null;
let tradingProcess = null;
let isTrading = false;

const isDev = process.env.ELECTRON_IS_DEV === 'true';

function createWindow() {
  // Create the browser window
  mainWindow = new BrowserWindow({
    width: 1400,
    height: 900,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, 'preload.js')
    },
    icon: path.join(__dirname, '../static/icon-192x192.png'),
    show: false,
    title: 'HERMUS Trading Bot'
  });

  // Load the app
  if (isDev) {
    mainWindow.loadURL('http://localhost:5173');
    mainWindow.webContents.openDevTools();
  } else {
    mainWindow.loadFile(path.join(__dirname, 'dist/index.html'));
  }

  // Show window when ready
  mainWindow.once('ready-to-show', () => {
    mainWindow.show();
  });

  // Handle window closed
  mainWindow.on('closed', () => {
    mainWindow = null;
  });

  // Minimize to tray instead of closing
  mainWindow.on('close', (event) => {
    if (!app.isQuiting) {
      event.preventDefault();
      mainWindow.hide();
      showNotification('HERMUS Trading Bot', 'Application minimized to system tray');
    }
  });
}

function createTray() {
  const trayIconPath = path.join(__dirname, '../static/icon-192x192.png');
  
  // Fallback to a simple icon if the main one doesn't exist
  const iconPath = fs.existsSync(trayIconPath) ? trayIconPath : null;
  
  if (iconPath) {
    tray = new Tray(iconPath);
  } else {
    // Create a simple tray without icon for now
    tray = new Tray(path.join(__dirname, '../static/favicon.png'));
  }

  const contextMenu = Menu.buildFromTemplate([
    {
      label: 'Show Dashboard',
      click: () => {
        if (mainWindow) {
          mainWindow.show();
          mainWindow.focus();
        } else {
          createWindow();
        }
      }
    },
    { type: 'separator' },
    {
      label: isTrading ? 'Stop Trading' : 'Start Trading',
      click: () => {
        if (isTrading) {
          stopTrading();
        } else {
          startTrading();
        }
      }
    },
    {
      label: 'Trading Status',
      submenu: [
        {
          label: isTrading ? 'Status: ACTIVE' : 'Status: STOPPED',
          enabled: false
        },
        {
          label: 'View Logs',
          click: () => {
            shell.openPath(path.join(__dirname, '../logs'));
          }
        }
      ]
    },
    { type: 'separator' },
    {
      label: 'Settings',
      click: () => {
        if (mainWindow) {
          mainWindow.show();
          mainWindow.focus();
          mainWindow.webContents.send('navigate-to', '/settings');
        }
      }
    },
    {
      label: 'Emergency Stop',
      click: () => {
        emergencyStop();
      }
    },
    { type: 'separator' },
    {
      label: 'Exit',
      click: () => {
        app.isQuiting = true;
        if (tradingProcess) {
          stopTrading();
        }
        app.quit();
      }
    }
  ]);

  tray.setContextMenu(contextMenu);
  tray.setToolTip('HERMUS Trading Bot');

  tray.on('double-click', () => {
    if (mainWindow) {
      mainWindow.show();
      mainWindow.focus();
    } else {
      createWindow();
    }
  });
}

function startTrading() {
  if (tradingProcess) {
    showNotification('HERMUS Trading Bot', 'Trading is already running');
    return;
  }

  const pythonScript = path.join(__dirname, '../main_unified_system.py');
  
  // Start the trading bot process
  tradingProcess = spawn('python', [pythonScript], {
    cwd: path.join(__dirname, '..'),
    stdio: ['pipe', 'pipe', 'pipe']
  });

  tradingProcess.stdout.on('data', (data) => {
    console.log(`Trading Bot: ${data}`);
    if (mainWindow) {
      mainWindow.webContents.send('trading-log', data.toString());
    }
  });

  tradingProcess.stderr.on('data', (data) => {
    console.error(`Trading Bot Error: ${data}`);
    if (mainWindow) {
      mainWindow.webContents.send('trading-error', data.toString());
    }
  });

  tradingProcess.on('close', (code) => {
    console.log(`Trading bot process exited with code ${code}`);
    isTrading = false;
    tradingProcess = null;
    updateTrayMenu();
    
    if (mainWindow) {
      mainWindow.webContents.send('trading-status', { isTrading: false, code });
    }
    
    if (code !== 0) {
      showNotification('HERMUS Trading Bot', `Trading stopped unexpectedly (code: ${code})`);
    }
  });

  isTrading = true;
  updateTrayMenu();
  showNotification('HERMUS Trading Bot', 'Trading started successfully');
  
  if (mainWindow) {
    mainWindow.webContents.send('trading-status', { isTrading: true });
  }
}

function stopTrading() {
  if (!tradingProcess) {
    showNotification('HERMUS Trading Bot', 'Trading is not running');
    return;
  }

  tradingProcess.kill('SIGTERM');
  
  setTimeout(() => {
    if (tradingProcess) {
      tradingProcess.kill('SIGKILL');
    }
  }, 5000);

  isTrading = false;
  tradingProcess = null;
  updateTrayMenu();
  showNotification('HERMUS Trading Bot', 'Trading stopped');
  
  if (mainWindow) {
    mainWindow.webContents.send('trading-status', { isTrading: false });
  }
}

function emergencyStop() {
  if (tradingProcess) {
    tradingProcess.kill('SIGKILL');
    tradingProcess = null;
  }
  
  isTrading = false;
  updateTrayMenu();
  showNotification('HERMUS Trading Bot', 'EMERGENCY STOP ACTIVATED');
  
  if (mainWindow) {
    mainWindow.webContents.send('trading-status', { isTrading: false, emergency: true });
  }
}

function updateTrayMenu() {
  if (tray) {
    createTray(); // Recreate tray menu with updated status
  }
}

function showNotification(title, body) {
  if (Notification.isSupported()) {
    new Notification({
      title,
      body,
      icon: path.join(__dirname, '../static/icon-192x192.png')
    }).show();
  }
}

// IPC handlers
ipcMain.handle('start-trading', () => {
  startTrading();
});

ipcMain.handle('stop-trading', () => {
  stopTrading();
});

ipcMain.handle('get-trading-status', () => {
  return { isTrading, processId: tradingProcess ? tradingProcess.pid : null };
});

ipcMain.handle('emergency-stop', () => {
  emergencyStop();
});

ipcMain.handle('minimize-to-tray', () => {
  if (mainWindow) {
    mainWindow.hide();
  }
});

ipcMain.handle('show-window', () => {
  if (mainWindow) {
    mainWindow.show();
    mainWindow.focus();
  } else {
    createWindow();
  }
});

ipcMain.handle('open-logs-folder', () => {
  shell.openPath(path.join(__dirname, '../logs'));
});

ipcMain.handle('get-settings', () => {
  // Return default settings for now
  return {
    autoStart: false,
    minimizeToTray: true,
    notifications: true,
    theme: 'dark'
  };
});

ipcMain.handle('save-settings', (event, settings) => {
  // Save settings logic here
  console.log('Saving settings:', settings);
  return true;
});

ipcMain.handle('get-system-stats', () => {
  return {
    cpu: Math.random() * 100,
    memory: Math.random() * 100,
    uptime: process.uptime()
  };
});

// App event handlers
app.whenReady().then(() => {
  createWindow();
  createTray();

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
    }
  });
});

app.on('window-all-closed', () => {
  // Keep app running in system tray on Windows/Linux
  if (process.platform !== 'darwin') {
    // Don't quit, just hide
  }
});

app.on('before-quit', () => {
  app.isQuiting = true;
  if (tradingProcess) {
    stopTrading();
  }
});
