# HERMUS Trading Bot - Desktop Application

Professional desktop application wrapper for the HERMUS Bybit Trading Bot with system tray integration and independent operation.

## Features

### System Tray Integration
- Always-running system tray icon with HERMUS logo
- Right-click context menu with trading controls
- Status indicators for trading state
- Desktop notifications for trading events
- Minimize to tray functionality

### Trading Controls
- Start/Stop trading from system tray or dashboard
- Emergency stop functionality
- Real-time trading status monitoring
- Process management for backend trading system

### Desktop UI
- Professional Electron wrapper with Material-UI
- Real-time trading dashboard with charts
- Live log streaming from backend
- Offline mode handling when backend unavailable
- Window management (minimize to tray, always on top)

### Process Management
- Automatic backend process spawning
- Process monitoring and health checks
- Graceful shutdown handling
- Error recovery and restart capabilities

## Installation & Setup

### Prerequisites
- Node.js and npm installed in bybit-trader conda environment
- HERMUS trading system (main_unified_system.py) available
- Windows operating system (primary target)

### Quick Setup
1. Run the complete setup script:
   ```bash
   setup_complete_desktop.bat
   ```

### Manual Setup
1. Navigate to desktop-app directory:
   ```bash
   cd desktop-app
   ```

2. Install dependencies:
   ```bash
   npm install
   ```

3. Build the React application:
   ```bash
   npm run build
   ```

## Usage

### Production Mode
Launch the desktop application:
```bash
launch_hermus_desktop.bat
```

### Development Mode
Start with hot reload and DevTools:
```bash
dev_hermus_desktop.bat
```

### Component Sync
Sync components from gui/ directory:
```bash
copy_frontend_components.bat
```

## System Tray Menu

Right-click the system tray icon to access:
- **Show Dashboard**: Open the main application window
- **Start Trading**: Begin automated trading
- **Stop Trading**: Stop all trading activities
- **Trading Status**: View current status and logs
- **Settings**: Configure application preferences
- **Emergency Stop**: Immediate halt of all operations
- **Exit**: Close application completely

## Architecture

### Main Process (main.js)
- Electron main process
- System tray management
- Backend process spawning
- IPC communication handling
- Window management

### Renderer Process (React App)
- Material-UI based dashboard
- Real-time trading charts
- Log display and monitoring
- Trading controls interface
- Status indicators

### Preload Script (preload.js)
- Secure IPC bridge
- Context isolation
- API exposure to renderer

## File Structure

```
desktop-app/
├── main.js                 # Electron main process
├── preload.js             # Security bridge
├── package.json           # Dependencies and scripts
├── vite.config.js         # Build configuration
├── index.html             # Entry point
└── src/
    ├── App.jsx            # Main React application
    ├── main.jsx           # React entry point
    ├── contexts/
    │   └── ElectronContext.jsx  # Desktop context
    └── components/
        ├── TradingChart.jsx     # Live trading chart
        └── TradingDashboard.jsx # Main dashboard
```

## Integration Points

### Backend Connection
- Connects to existing main_unified_system.py
- Uses existing API endpoints
- Preserves all trading functionality
- Maintains SuperGPT AI systems

### Logo and Branding
- Uses existing logo.png for all branding
- Consistent visual identity
- Professional appearance

### Configuration
- Integrates with existing config system
- Preserves user settings
- Maintains security protocols

## Development

### Available Scripts
- `npm run dev`: Development mode with hot reload
- `npm run build`: Build for production
- `npm run electron`: Start Electron app
- `npm run pack`: Package for distribution
- `npm run dist`: Create installer

### Building for Distribution
```bash
npm run dist-win
```

Creates Windows installer in `dist/` directory.

## Troubleshooting

### Common Issues

1. **Vite command not found**
   - Ensure Node.js is properly installed in conda environment
   - Run `npm install` in desktop-app directory

2. **Backend connection failed**
   - Verify main_unified_system.py is accessible
   - Check Python environment activation
   - Ensure all dependencies are installed

3. **System tray icon not appearing**
   - Check logo file exists in static/ directory
   - Verify Windows system tray settings
   - Restart application

4. **Trading controls not working**
   - Ensure backend process permissions
   - Check conda environment activation
   - Verify Python script paths

### Logs and Debugging
- Application logs: Check console in DevTools
- Backend logs: Available in logs/ directory
- System logs: Windows Event Viewer

## Security

- Context isolation enabled
- Node integration disabled
- Secure IPC communication
- No remote module access
- Preload script validation

## Performance

- Optimized React components
- Efficient IPC communication
- Minimal resource usage
- Background process management
- Memory leak prevention

## Support

For issues and support:
1. Check troubleshooting section
2. Review application logs
3. Verify system requirements
4. Contact development team

## License

Part of the HERMUS Trading Bot system.
All rights reserved.
