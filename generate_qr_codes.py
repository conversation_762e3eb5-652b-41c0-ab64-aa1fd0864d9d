#!/usr/bin/env python3
"""
QR Code Generator for Bybit Trading Bot Mobile Installation
Generates QR codes for easy phone access to the trading bot
"""

import qrcode
from qrcode.image.styledpil import StyledPilImage
from qrcode.image.styles.moduledrawers.pil import RoundedModuleDrawer
from qrcode.image.styles.colorfills import SquareGradiantColorFill
from pathlib import Path

def create_trading_bot_qr():
    """Create QR code for trading bot access"""
    
    # Trading bot URL
    url = "http://91.179.83.180:8000"
    
    # Create QR code instance
    qr = qrcode.QRCode(
        version=1,
        error_correction=qrcode.constants.ERROR_CORRECT_H,
        box_size=10,
        border=4,
    )
    
    # Add URL data
    qr.add_data(url)
    qr.make(fit=True)
    
    # Create styled QR code image
    img = qr.make_image(
        image_factory=StyledPilImage,
        module_drawer=RoundedModuleDrawer(),
        color_mask=SquareGradiantColorFill(
            back_color=(10, 10, 10),     # Dark background
            center_color=(0, 255, 136),   # Bright green center
            edge_color=(0, 204, 106)      # Darker green edge
        )
    )
    
    # Save QR code
    output_dir = Path("frontend/qr-codes")
    output_dir.mkdir(exist_ok=True)
    
    qr_path = output_dir / "trading-bot-qr.png"
    img.save(qr_path)
    
    print(f"✅ QR code saved: {qr_path}")
    return qr_path

def create_installation_qr():
    """Create QR code for installation page"""
    
    url = "http://91.179.83.180:8000/mobile-install"
    
    qr = qrcode.QRCode(
        version=1,
        error_correction=qrcode.constants.ERROR_CORRECT_H,
        box_size=10,
        border=4,
    )
    
    qr.add_data(url)
    qr.make(fit=True)
    
    # Create image with custom colors
    img = qr.make_image(
        fill_color="#00ff88",
        back_color="#0a0a0a"
    )
    
    output_dir = Path("frontend/qr-codes")
    output_dir.mkdir(exist_ok=True)
    
    qr_path = output_dir / "installation-qr.png" 
    img.save(qr_path)
    
    print(f"✅ Installation QR code saved: {qr_path}")
    return qr_path

def create_emergency_qr():
    """Create QR code for emergency stop"""
    
    url = "http://91.179.83.180:8000/?action=stop"
    
    qr = qrcode.QRCode(
        version=1,
        error_correction=qrcode.constants.ERROR_CORRECT_H,
        box_size=8,
        border=3,
    )
    
    qr.add_data(url)
    qr.make(fit=True)
    
    # Red color for emergency
    img = qr.make_image(
        fill_color="#ff4444",
        back_color="#0a0a0a"
    )
    
    output_dir = Path("frontend/qr-codes")
    output_dir.mkdir(exist_ok=True)
    
    qr_path = output_dir / "emergency-stop-qr.png"
    img.save(qr_path)
    
    print(f"✅ Emergency QR code saved: {qr_path}")
    return qr_path

def generate_html_qr_page():
    """Generate HTML page with all QR codes"""
    
    html_content = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Trading Bot QR Codes</title>
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background: #0a0a0a;
            color: #ffffff;
            padding: 20px;
            text-align: center;
        }
        .qr-container {
            display: inline-block;
            margin: 20px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 15px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        .qr-title {
            color: #00ff88;
            margin-bottom: 15px;
            font-weight: 600;
        }
        .qr-code img {
            max-width: 250px;
            border-radius: 10px;
        }
        .qr-url {
            font-family: monospace;
            background: #000;
            padding: 8px;
            border-radius: 5px;
            margin-top: 10px;
            font-size: 12px;
            word-break: break-all;
        }
        h1 {
            color: #00ff88;
            margin-bottom: 30px;
        }
    </style>
</head>
<body>
    <h1>📱 Bybit Trading Bot - QR Codes</h1>
    
    <div class="qr-container">
        <h3 class="qr-title">🚀 Main Trading Bot</h3>
        <div class="qr-code">
            <img src="qr-codes/trading-bot-qr.png" alt="Trading Bot QR">
        </div>
        <div class="qr-url">http://91.179.83.180:8000</div>
        <p>Scan to access the main trading dashboard</p>
    </div>
    
    <div class="qr-container">
        <h3 class="qr-title">📲 Installation Guide</h3>
        <div class="qr-code">
            <img src="qr-codes/installation-qr.png" alt="Installation QR">
        </div>
        <div class="qr-url">http://91.179.83.180:8000/mobile-install</div>
        <p>Scan for installation instructions</p>
    </div>
    
    <div class="qr-container">
        <h3 class="qr-title">🚨 Emergency Stop</h3>
        <div class="qr-code">
            <img src="qr-codes/emergency-stop-qr.png" alt="Emergency QR">
        </div>
        <div class="qr-url">http://91.179.83.180:8000/?action=stop</div>
        <p>Scan for emergency trading stop</p>
    </div>
    
    <div style="margin-top: 40px; color: #a0a0a0;">
        <p>📱 Point your phone's camera at any QR code to access instantly</p>
        <p>💡 For best experience, install as PWA (Progressive Web App)</p>
    </div>
</body>
</html>
"""
    
    with open("frontend/qr-codes.html", "w") as f:
        f.write(html_content)
    
    print("✅ QR codes HTML page created: frontend/qr-codes.html")

def main():
    """Generate all QR codes and HTML page"""
    
    print("🎯 Generating QR codes for Bybit Trading Bot...")
    print("=" * 50)
    
    # Create QR codes directory
    Path("frontend/qr-codes").mkdir(exist_ok=True)
    
    try:
        # Generate QR codes
        create_trading_bot_qr()
        create_installation_qr()
        create_emergency_qr()
        
        # Generate HTML page
        generate_html_qr_page()
        
        print("=" * 50)
        print("🎉 SUCCESS! All QR codes generated")
        print()
        print("📁 Files created:")
        print("   - frontend/qr-codes/trading-bot-qr.png")
        print("   - frontend/qr-codes/installation-qr.png") 
        print("   - frontend/qr-codes/emergency-stop-qr.png")
        print("   - frontend/qr-codes.html")
        print()
        print("📱 Usage:")
        print("   1. Open frontend/qr-codes.html in browser")
        print("   2. Scan QR codes with phone camera")
        print("   3. Instant access to trading bot!")
        print()
        print("💡 Or run: easy-phone-install.bat for guided setup")
        
    except ImportError:
        print("❌ qrcode library not found")
        print()
        print("📦 Install with:")
        print("   pip install qrcode[pil]")
        print()
        print("🔧 Then run this script again")
        
    except Exception as e:
        print(f"❌ Error generating QR codes: {e}")

if __name__ == "__main__":
    main()
