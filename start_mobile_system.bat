@echo off
REM PERMANENT MOBILE APP - QUICK LAUNCHER
REM Start the trading system with mobile control capabilities

title Bybit Trading Bot - Mobile Control System

echo ================================================================
echo PERMANENT MOBILE APP - QUICK LAUNCHER
echo ================================================================
echo [LAUNCHER] Starting trading system with mobile control
echo [LAUNCHER] Mobile app will be available at http://*************:8000/mobile
echo [LAUNCHER] System runs independently - mobile app is optional
echo ================================================================

REM Change to the correct directory
cd /d "e:\The_real_deal_copy\Bybit_Bot\BOT"

REM Activate conda environment
echo [LAUNCHER] Activating conda environment...
call E:\conda\scripts\activate_bybit_trader.bat

REM Check activation
if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] Failed to activate environment
    echo [INFO] Trying alternative method...
    call conda activate bybit-trader
)

REM Show system info
echo [LAUNCHER] Environment: %CONDA_DEFAULT_ENV%
echo [LAUNCHER] Python: 
python --version
echo [LAUNCHER] Directory: %CD%

REM Start the main system with mobile capabilities
echo [LAUNCHER] Starting system with mobile control...
echo [LAUNCHER] Mobile app: http://*************:8000/mobile
echo [LAUNCHER] Control panel: http://*************:8000/control
echo [LAUNCHER] Installation guide: http://*************:8000/install-guide
echo ================================================================

python main_unified_system.py

echo [LAUNCHER] System stopped
pause
