#!/usr/bin/env python3
"""
AUTONOMOUS TRADING SYSTEM - INDEPENDENT LAUNCHER
This script ensures the trading system runs independently of the mobile app.
The system operates autonomously even without mobile interface.
"""

import asyncio
import sys
import os
import signal
import logging
from pathlib import Path

# Add the current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('trading_system.log'),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)

class IndependentTradingLauncher:
    """Independent launcher for the autonomous trading system"""
    
    def __init__(self):
        self.system = None
        self.running = False
        self.web_server = None
        
    async def start_trading_system(self):
        """Start the core trading system independently"""
        try:
            logger.info("[LAUNCHER] Starting Independent Trading System...")
            
            # Import the unified system
            from main_unified_system import UnifiedTradingSystem
            
            # Create system instance
            self.system = UnifiedTradingSystem()
            
            # Initialize the complete system
            await self.system.initialize_complete_system()
            
            logger.info("[LAUNCHER] Trading system initialized successfully")
            logger.info("[LAUNCHER] System is now running AUTONOMOUSLY")
            logger.info("[LAUNCHER] Mobile app is optional - system operates independently")
            
            self.running = True
            return True
            
        except Exception as e:
            logger.error(f"[LAUNCHER] Failed to start trading system: {e}")
            return False
    
    async def start_web_interface(self):
        """Start the web interface (optional for mobile access)"""
        try:
            logger.info("[LAUNCHER] Starting Web Interface for Mobile Access...")
            
            import uvicorn
            from main_unified_system import app
            
            # Start the web server in the background
            config = uvicorn.Config(
                app=app,
                host="0.0.0.0",  # Listen on all interfaces
                port=8000,
                log_level="info",
                access_log=True
            )
            
            server = uvicorn.Server(config)
            
            # Run server in background task
            self.web_server = asyncio.create_task(server.serve())
            
            logger.info("[LAUNCHER] Web interface started on http://0.0.0.0:8000")
            logger.info("[LAUNCHER] Mobile app available at: http://*************:8000/mobile")
            logger.info("[LAUNCHER] System control via: http://*************:8000/control")
            
            return True
            
        except Exception as e:
            logger.error(f"[LAUNCHER] Failed to start web interface: {e}")
            logger.info("[LAUNCHER] Trading system continues to run independently")
            return False
    
    async def run_autonomous_loop(self):
        """Main autonomous trading loop"""
        logger.info("[LAUNCHER] Starting Autonomous Trading Loop...")
        
        while self.running:
            try:
                # System health check
                if self.system:
                    # Let the system run its autonomous operations
                    await asyncio.sleep(1)  # Non-blocking sleep
                else:
                    logger.warning("[LAUNCHER] System instance lost - attempting restart...")
                    await self.start_trading_system()
                    
            except KeyboardInterrupt:
                logger.info("[LAUNCHER] Shutdown signal received")
                break
            except Exception as e:
                logger.error(f"[LAUNCHER] Error in autonomous loop: {e}")
                await asyncio.sleep(5)  # Brief pause before retry
    
    async def shutdown(self):
        """Graceful shutdown"""
        logger.info("[LAUNCHER] Shutting down system...")
        
        self.running = False
        
        # Stop web server if running
        if self.web_server:
            self.web_server.cancel()
            logger.info("[LAUNCHER] Web interface stopped")
        
        # Stop trading system
        if self.system:
            try:
                await self.system.shutdown()
                logger.info("[LAUNCHER] Trading system stopped")
            except Exception as e:
                logger.error(f"[LAUNCHER] Error stopping trading system: {e}")
        
        logger.info("[LAUNCHER] Shutdown complete")

def signal_handler(signum, frame):
    """Handle shutdown signals"""
    print(f"\n[LAUNCHER] Received signal {signum}")
    raise KeyboardInterrupt

async def main():
    """Main entry point"""
    logger.info("="*60)
    logger.info("AUTONOMOUS TRADING SYSTEM - INDEPENDENT LAUNCHER")
    logger.info("="*60)
    logger.info("[LAUNCHER] System designed to run independently")
    logger.info("[LAUNCHER] Mobile app provides optional control interface")
    logger.info("[LAUNCHER] Trading continues autonomously without mobile app")
    logger.info("="*60)
    
    # Set up signal handlers
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    launcher = IndependentTradingLauncher()
    
    try:
        # Start the core trading system (REQUIRED)
        trading_started = await launcher.start_trading_system()
        
        if not trading_started:
            logger.error("[LAUNCHER] Failed to start trading system - exiting")
            return
        
        # Start web interface (OPTIONAL)
        web_started = await launcher.start_web_interface()
        
        if web_started:
            logger.info("[LAUNCHER] Full system operational - trading + web interface")
        else:
            logger.info("[LAUNCHER] Trading system running independently (no web interface)")
        
        # Run autonomous trading loop
        await launcher.run_autonomous_loop()
        
    except KeyboardInterrupt:
        logger.info("[LAUNCHER] Interrupt received - initiating shutdown")
    except Exception as e:
        logger.error(f"[LAUNCHER] Unexpected error: {e}")
    finally:
        await launcher.shutdown()

if __name__ == "__main__":
    """
    INDEPENDENT OPERATION GUARANTEE:
    - Trading system runs autonomously without mobile app
    - Mobile app provides optional control interface
    - System continues trading even if mobile app is closed
    - All autonomous functions remain active
    """
    
    # Ensure we're in the right directory
    os.chdir(Path(__file__).parent)
    
    # Run the independent system
    try:
        asyncio.run(main())
    except Exception as e:
        logger.error(f"[LAUNCHER] Fatal error: {e}")
        sys.exit(1)
