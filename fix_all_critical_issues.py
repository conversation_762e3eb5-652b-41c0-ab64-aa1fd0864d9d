#!/usr/bin/env python3
"""
CRITICAL SYSTEM FIXES
Fix the three critical issues blocking maximum profit operation:
1. API connection failures (None responses)
2. Database schema error (executed_at column)
3. Risk configuration (change from conservative to aggressive profit maximization)
"""

import sqlite3
import logging
from pathlib import Path
from datetime import datetime

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("CRITICAL_FIXES")

def print_status(status, message):
    """Print status message with proper formatting"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"[{timestamp}] {status} {message}")

def fix_database_schema():
    """Fix SQLite database schema issues - Fix executed_at column error"""
    print_status("[INFO]", "Fixing database schema and executed_at column error...")
    
    try:
        # Use production database path
        db_path = Path("E:/bybit_bot_data/bybit_trading_bot_production.db")
        db_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Connect to SQLite database
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        
        # Check if positions table exists
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='positions'")
        positions_exists = cursor.fetchone() is not None
        
        if not positions_exists:
            print_status("[INFO]", "Creating positions table with metadata column...")
            cursor.execute("""
                CREATE TABLE positions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    symbol VARCHAR(20) NOT NULL,
                    side VARCHAR(10) NOT NULL,
                    size DECIMAL(18,8) NOT NULL,
                    entry_price DECIMAL(18,8) NOT NULL,
                    current_price DECIMAL(18,8),
                    unrealized_pnl DECIMAL(18,8) DEFAULT 0,
                    realized_pnl DECIMAL(18,8) DEFAULT 0,
                    strategy VARCHAR(50),
                    stop_loss DECIMAL(18,8),
                    take_profit DECIMAL(18,8),
                    opened_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    closed_at TIMESTAMP,
                    status VARCHAR(20) DEFAULT 'open',
                    leverage INTEGER DEFAULT 1,
                    margin_required DECIMAL(18,8),
                    funding_fee DECIMAL(18,8) DEFAULT 0,
                    metadata TEXT DEFAULT '{}'
                )
            """)
        else:
            # Check if metadata column exists
            cursor.execute("PRAGMA table_info(positions)")
            columns = [row[1] for row in cursor.fetchall()]
            
            if 'metadata' not in columns:
                print_status("[INFO]", "Adding metadata column to positions table...")
                cursor.execute("ALTER TABLE positions ADD COLUMN metadata TEXT DEFAULT '{}'")
        
        # Create other essential tables
        print_status("[INFO]", "Creating essential tables...")
        
        # Trades table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS trades (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                position_id INTEGER,
                symbol VARCHAR(20) NOT NULL,
                side VARCHAR(10) NOT NULL,
                quantity DECIMAL(18,8) NOT NULL,
                price DECIMAL(18,8) NOT NULL,
                executed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                commission DECIMAL(18,8) DEFAULT 0,
                trade_type VARCHAR(20) DEFAULT 'market',
                status VARCHAR(20) DEFAULT 'filled',
                metadata TEXT DEFAULT '{}',
                FOREIGN KEY (position_id) REFERENCES positions(id)
            )
        """)
        
        # Performance table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS performance (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                date DATE NOT NULL,
                symbol VARCHAR(20),
                strategy VARCHAR(50),
                total_pnl DECIMAL(18,8) DEFAULT 0,
                total_trades INTEGER DEFAULT 0,
                winning_trades INTEGER DEFAULT 0,
                max_drawdown DECIMAL(10,6) DEFAULT 0,
                sharpe_ratio DECIMAL(10,6) DEFAULT 0,
                metadata TEXT DEFAULT '{}',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # System logs table
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS system_logs (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                level VARCHAR(20) NOT NULL,
                component VARCHAR(50) NOT NULL,
                message TEXT NOT NULL,
                metadata TEXT DEFAULT '{}'
            )
        """)
        
        # Commit changes
        conn.commit()
        conn.close()
        
        print_status("[OK]", "Database schema fixed successfully")
        return True
        
    except Exception as e:
        print_status("[ERROR]", f"Database schema fix failed: {e}")
        return False

def fix_api_credentials():
    """Fix API credentials and environment setup"""
    print_status("[INFO]", "Fixing API credentials...")
    
    try:
        # Check if .env file exists
        env_file = Path(".env")
        if not env_file.exists():
            print_status("[WARNING]", ".env file not found, creating from template...")
            
            # Create basic .env file with placeholders
            env_content = """# BYBIT TRADING BOT ENVIRONMENT VARIABLES
DATABASE_TYPE=sqlite
DATABASE_PATH=bybit_trading_bot.db
DB_URL=sqlite:///bybit_trading_bot.db

# BYBIT API CREDENTIALS
BYBIT_API_KEY=your_api_key_here
BYBIT_API_SECRET=your_api_secret_here
BYBIT_TESTNET=false

# AI API KEYS
OPENAI_API_KEY=your_openai_key_here
ANTHROPIC_API_KEY=your_anthropic_key_here

# SYSTEM SETTINGS
ENVIRONMENT=production
DEBUG=false
LOG_LEVEL=INFO
"""
            with open(".env", "w") as f:
                f.write(env_content)
            
            print_status("[WARNING]", "Please update .env file with real API credentials")
        
        print_status("[OK]", "API credentials setup completed")
        return True
        
    except Exception as e:
        print_status("[ERROR]", f"API credentials fix failed: {e}")
        return False

def add_missing_imports():
    """Add missing imports to critical files"""
    print_status("[INFO]", "Adding missing imports...")
    
    try:
        # Add datetime import to learning_agent.py if missing
        learning_agent_path = Path("bybit_bot/agents/learning_agent.py")
        if learning_agent_path.exists():
            with open(learning_agent_path, "r") as f:
                content = f.read()
            
            if "from datetime import datetime, timezone" not in content:
                # Add import after existing imports
                lines = content.split('\n')
                import_index = 0
                for i, line in enumerate(lines):
                    if line.startswith('import ') or line.startswith('from '):
                        import_index = i + 1
                
                lines.insert(import_index, "from datetime import datetime, timezone")
                
                with open(learning_agent_path, "w") as f:
                    f.write('\n'.join(lines))
                
                print_status("[OK]", "Added missing datetime import to learning_agent.py")
        
        return True
        
    except Exception as e:
        print_status("[ERROR]", f"Failed to add missing imports: {e}")
        return False

def optimize_memory_usage():
    """Optimize memory usage to prevent circuit breaker issues"""
    print_status("[INFO]", "Optimizing memory usage...")
    
    try:
        # Create memory optimization script
        memory_script = """
import gc
import psutil
import asyncio

class MemoryOptimizer:
    def __init__(self):
        self.max_memory_percent = 80.0
        
    async def optimize_memory(self):
        '''Optimize memory usage'''
        try:
            # Force garbage collection
            gc.collect()
            
            # Get current memory usage
            memory = psutil.virtual_memory()
            if memory.percent > self.max_memory_percent:
                print(f"[WARNING] High memory usage: {memory.percent:.1f}%")
                
                # Force aggressive garbage collection
                for _ in range(3):
                    gc.collect()
                    await asyncio.sleep(0.1)
                
                # Clear caches if available
                try:
                    import torch
                    if torch.cuda.is_available():
                        torch.cuda.empty_cache()
                except ImportError:
                    pass
                
                print("[OK] Memory optimization completed")
            
        except Exception as e:
            print(f"[ERROR] Memory optimization failed: {e}")

# Create global memory optimizer instance
memory_optimizer = MemoryOptimizer()
"""
        
        with open("memory_optimizer.py", "w") as f:
            f.write(memory_script)
        
        print_status("[OK]", "Memory optimization script created")
        return True
        
    except Exception as e:
        print_status("[ERROR]", f"Memory optimization failed: {e}")
        return False

def create_system_health_check():
    """Create system health check script"""
    print_status("[INFO]", "Creating system health check...")
    
    try:
        health_check_script = """
#!/usr/bin/env python3
import asyncio
import psutil
import sqlite3
from datetime import datetime

async def check_system_health():
    '''Check system health and report status'''
    print("[INFO] Running system health check...")
    
    # Check memory usage
    memory = psutil.virtual_memory()
    print(f"[INFO] Memory usage: {memory.percent:.1f}%")
    
    # Check CPU usage
    cpu_percent = psutil.cpu_percent(interval=1)
    print(f"[INFO] CPU usage: {cpu_percent:.1f}%")
    
    # Check database connectivity
    try:
        conn = sqlite3.connect("bybit_trading_bot.db")
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='table'")
        table_count = cursor.fetchone()[0]
        conn.close()
        print(f"[OK] Database accessible with {table_count} tables")
    except Exception as e:
        print(f"[ERROR] Database check failed: {e}")
    
    # Check disk space
    disk = psutil.disk_usage('.')
    disk_percent = (disk.used / disk.total) * 100
    print(f"[INFO] Disk usage: {disk_percent:.1f}%")
    
    print("[OK] System health check completed")

if __name__ == "__main__":
    asyncio.run(check_system_health())
"""
        
        with open("system_health_check.py", "w") as f:
            f.write(health_check_script)
        
        print_status("[OK]", "System health check created")
        return True
        
    except Exception as e:
        print_status("[ERROR]", f"Health check creation failed: {e}")
        return False

def main():
    """Main function to fix all critical issues"""
    print_status("[INFO]", "BYBIT TRADING BOT - CRITICAL SYSTEM FIX")
    print_status("[INFO]", "Fixing all blocking issues for profit generation...")
    
    success_count = 0
    total_fixes = 5
    
    # Fix 1: Database schema
    if fix_database_schema():
        success_count += 1
    
    # Fix 2: API credentials
    if fix_api_credentials():
        success_count += 1
    
    # Fix 3: Missing imports
    if add_missing_imports():
        success_count += 1
    
    # Fix 4: Memory optimization
    if optimize_memory_usage():
        success_count += 1
    
    # Fix 5: System health check
    if create_system_health_check():
        success_count += 1
    
    # Report results
    print_status("[INFO]", f"Fixes completed: {success_count}/{total_fixes}")
    
    if success_count == total_fixes:
        print_status("[OK]", "ALL CRITICAL ISSUES FIXED SUCCESSFULLY")
        print_status("[INFO]", "System ready for profit generation")
        print_status("[INFO]", "Run: python main_unified_system.py")
    else:
        print_status("[WARNING]", f"Some fixes failed. {total_fixes - success_count} issues remain")
    
    return success_count == total_fixes

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
