#!/usr/bin/env python3
"""
Test the mobile app server with logo
"""
import asyncio
import subprocess
import time
import requests
from pathlib import Path

def test_mobile_app_with_logo():
    print("🧪 Testing Mobile App with New Logo")
    print("=" * 50)
    
    # Check if logo files exist
    logo_svg = Path("static/logo.svg")
    icon_svg = Path("static/icon-192x192.svg")
    
    print(f"📱 Logo SVG exists: {logo_svg.exists()}")
    print(f"📱 Icon SVG exists: {icon_svg.exists()}")
    
    if not logo_svg.exists():
        print("❌ Logo file missing!")
        return False
    
    # Start mobile app server in background
    print("\n🚀 Starting mobile app server...")
    try:
        # Start server
        proc = subprocess.Popen([
            "python", "mobile_app_server.py"
        ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
        
        # Give server time to start
        time.sleep(3)
        
        # Test endpoints
        print("\n🔍 Testing endpoints...")
        
        # Test logo endpoint
        try:
            response = requests.get("http://localhost:8000/static/logo.svg", timeout=5)
            print(f"✅ Logo endpoint: {response.status_code}")
            if response.status_code == 200:
                print(f"   Logo size: {len(response.content)} bytes")
        except Exception as e:
            print(f"❌ Logo endpoint failed: {e}")
        
        # Test mobile app
        try:
            response = requests.get("http://localhost:8000/mobile", timeout=5)
            print(f"✅ Mobile app: {response.status_code}")
            if response.status_code == 200:
                if "Trading Bot Logo" in response.text:
                    print("✅ Logo reference found in HTML!")
                else:
                    print("⚠️  Logo reference not found in HTML")
        except Exception as e:
            print(f"❌ Mobile app failed: {e}")
        
        # Test manifest
        try:
            response = requests.get("http://localhost:8000/manifest.json", timeout=5)
            print(f"✅ Manifest: {response.status_code}")
            if response.status_code == 200:
                manifest = response.json()
                icons = manifest.get("icons", [])
                print(f"   Icons in manifest: {len(icons)}")
                for icon in icons:
                    print(f"   - {icon.get('src', 'unknown')} ({icon.get('sizes', 'unknown')})")
        except Exception as e:
            print(f"❌ Manifest failed: {e}")
        
        # Stop server
        proc.terminate()
        proc.wait(timeout=5)
        print("\n✅ Server stopped successfully")
        
        return True
        
    except Exception as e:
        print(f"❌ Server test failed: {e}")
        return False

if __name__ == "__main__":
    success = test_mobile_app_with_logo()
    if success:
        print("\n🎉 Mobile app with logo test completed!")
        print("\n📱 To access the app:")
        print("   1. Start: python mobile_app_server.py")
        print("   2. Visit: http://192.168.129.39:8000/mobile")
        print("   3. Add to home screen for permanent app!")
    else:
        print("\n❌ Test failed - check errors above")
