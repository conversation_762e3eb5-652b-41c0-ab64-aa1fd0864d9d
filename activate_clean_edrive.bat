@echo off
REM ============================================================================
REM CLEAN E-DRIVE CONDA SETUP WITHOUT CONDA INIT
REM ============================================================================
REM This script bypasses conda init and directly sets up the environment
REM ============================================================================

echo.
echo ================================================================================  
echo CLEAN E-DRIVE CONDA SETUP - NO CONDA INIT REQUIRED
echo ================================================================================

REM Change to project directory
cd /d "E:\The_real_deal_copy\Bybit_Bot\BOT"

REM Set E-drive conda paths
set "CONDA_ROOT=E:\conda\miniconda3"
set "CONDA_ENV_PATH=E:\conda\envs\bybit-trader"

REM Clean PATH - remove any existing conda/python paths that might conflict
set "CLEAN_PATH="
for %%i in ("%PATH:;=" "%") do (
    echo %%i | findstr /v /i "conda python anaconda miniconda" >nul
    if not errorlevel 1 (
        if defined CLEAN_PATH (
            set "CLEAN_PATH=!CLEAN_PATH!;%%~i"
        ) else (
            set "CLEAN_PATH=%%~i"
        )
    )
)

REM Set new clean PATH with E-drive conda first
set "PATH=%CONDA_ENV_PATH%;%CONDA_ENV_PATH%\Scripts;%CONDA_ENV_PATH%\Library\bin;%CONDA_ROOT%\Scripts;%CONDA_ROOT%\condabin;%CLEAN_PATH%"

REM Set conda environment variables
set "CONDA_PREFIX=%CONDA_ENV_PATH%"
set "CONDA_DEFAULT_ENV=bybit-trader"  
set "CONDA_PYTHON_EXE=%CONDA_ENV_PATH%\python.exe"
set "CONDA_PROMPT_MODIFIER=(bybit-trader) "

REM Set project environment variables
set "PYTHONPATH=E:\The_real_deal_copy\Bybit_Bot\BOT"
set "BYBIT_BOT_HOME=E:\The_real_deal_copy\Bybit_Bot\BOT"

REM Additional Python environment variables
set "PYTHONHOME=%CONDA_ENV_PATH%"
set "PYTHONEXECUTABLE=%CONDA_ENV_PATH%\python.exe"

echo [SUCCESS] Environment variables set
echo.
echo [VERIFICATION] Environment Status:
echo Python Executable: %CONDA_PYTHON_EXE%
echo Conda Environment: %CONDA_DEFAULT_ENV%
echo Project Directory: %BYBIT_BOT_HOME%
echo Current Directory: %CD%

echo.
echo [TEST] Testing Python:
"%CONDA_PYTHON_EXE%" --version
if errorlevel 1 (
    echo [ERROR] Python test failed
    exit /b 1
)

echo.
echo [TEST] Testing Python imports:
"%CONDA_PYTHON_EXE%" -c "import sys; print(f'Python Path: {sys.executable}'); import numpy, pandas, ccxt; print('All packages imported successfully')"
if errorlevel 1 (
    echo [ERROR] Package import test failed
    exit /b 1
)

echo.
echo [SUCCESS] Clean E-Drive Conda Environment Ready!
echo ================================================================================
