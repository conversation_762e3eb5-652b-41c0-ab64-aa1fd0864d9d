<?xml version="1.0" encoding="UTF-8"?>
<svg width="192" height="192" viewBox="0 0 192 192" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <radialGradient id="iconGradient" cx="50%" cy="30%" r="70%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.9"/>
      <stop offset="100%" style="stop-color:#000000;stop-opacity:1"/>
    </radialGradient>
    <linearGradient id="borderGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#00ff88"/>
      <stop offset="100%" style="stop-color:#00ccff"/>
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="192" height="192" rx="32" fill="url(#borderGradient)"/>
  <rect x="8" y="8" width="176" height="176" rx="24" fill="url(#iconGradient)"/>
  
  <!-- Main profile silhouette -->
  <path d="M56 64 Q72 40 96 40 Q120 40 136 64 Q140 80 136 104 Q128 120 112 128 Q96 136 80 128 Q64 120 56 104 Q48 80 56 64 Z" 
        fill="#000000" opacity="0.8"/>
  
  <!-- Hair detail -->
  <path d="M64 56 Q80 32 96 35 Q112 32 128 56 Q136 64 131 72 Q120 48 96 45 Q72 48 61 72 Q56 64 64 56 Z" 
        fill="#1a1a1a" opacity="0.9"/>
  
  <!-- Face profile -->
  <path d="M88 56 Q104 56 112 72 Q115 88 112 104 Q104 112 96 112 Q88 112 83 104 Q80 88 83 72 Q88 56 88 56 Z" 
        fill="#2d3748" opacity="0.7"/>
  
  <!-- Trading symbol -->
  <text x="96" y="152" font-family="Arial, sans-serif" font-size="20" font-weight="bold" 
        text-anchor="middle" fill="#00d4aa">$BOT</text>
  
  <!-- Inner accent border -->
  <rect x="8" y="8" width="176" height="176" rx="24" fill="none" stroke="#00d4aa" stroke-width="2" opacity="0.6"/>
</svg>
