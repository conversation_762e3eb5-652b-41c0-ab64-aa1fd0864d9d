#!/usr/bin/env python3
"""
Quick test of main system creation
"""
import asyncio
import sys

async def main():
    try:
        print("Testing main_unified_system import...")
        from main_unified_system import RealUnifiedTradingSystem
        print("✅ Import successful")
        
        print("Creating system instance...")
        system = RealUnifiedTradingSystem()
        print("✅ System created")
        
        print("🎉 All basic tests passed! System should be able to start.")
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
