#!/usr/bin/env python3
"""
Comprehensive fix for ALL configuration classes
Makes all dataclass configs flexible to handle any parameters
"""
import sys
import traceback
from pathlib import Path
import re

def add_flexible_init_to_dataclasses():
    """Add flexible __init__ to all dataclass configs"""
    config_file = Path("bybit_bot/core/config.py")
    
    print("[INFO] Adding flexible initialization to all dataclass configs...")
    
    try:
        # Read the current config file
        with open(config_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Template for flexible initialization
        flexible_init_template = '''    extra_params: Dict[str, Any] = field(default_factory=dict)
    
    def __init__(self, **kwargs):
        """Initialize with flexible parameter handling"""
        field_names = {f.name for f in fields(self)}
        
        # Initialize defaults
        for field_info in fields(self):
            if field_info.default_factory != dataclasses.MISSING:
                setattr(self, field_info.name, field_info.default_factory())
            elif field_info.default != dataclasses.MISSING:
                setattr(self, field_info.name, field_info.default)
        
        # Set provided values
        for key, value in kwargs.items():
            if key in field_names:
                setattr(self, key, value)
            else:
                self.extra_params[key] = value'''
        
        # List of dataclass names that need flexible init
        dataclass_names = [
            'SystemConfig',
            'SuperGPTConfig', 
            'OpenRouterConfig',
            'RiskManagementConfig',
            'PortfolioConfig',
            'StrategiesConfig',
            'ExecutionConfig',
            'DatabaseConfig',
            'MonitoringConfig'
        ]
        
        # Process each dataclass
        for class_name in dataclass_names:
            # Pattern to match the entire dataclass definition
            pattern = rf'(@dataclass\s*\nclass {class_name}:.*?)(?=@dataclass|class [A-Z]|\Z)'
            
            def replace_class(match):
                class_content = match.group(1)
                
                # Skip if already has flexible init
                if '__init__(self, **kwargs)' in class_content:
                    return class_content
                
                # Skip if already has extra_params
                if 'extra_params:' in class_content:
                    return class_content
                
                # Find the last field definition
                lines = class_content.split('\n')
                insert_index = -1
                
                for i, line in enumerate(lines):
                    if ':' in line and '=' in line and not line.strip().startswith('#'):
                        insert_index = i
                
                if insert_index != -1:
                    # Insert the flexible init after the last field
                    lines.insert(insert_index + 1, flexible_init_template)
                    return '\n'.join(lines)
                
                return class_content
            
            content = re.sub(pattern, replace_class, content, flags=re.DOTALL)
        
        # Write the fixed content back
        with open(config_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("[OK] Added flexible initialization to all dataclass configs")
        return True
        
    except Exception as e:
        print(f"[ERROR] Failed to add flexible initialization: {e}")
        traceback.print_exc()
        return False

def test_all_config_loading():
    """Test that all configurations can be loaded"""
    try:
        print("[INFO] Testing all configuration loading...")
        
        # Test the main config loading
        from bybit_bot.core.config import get_config
        config = get_config()
        
        print(f"[OK] Main configuration loaded successfully")
        print(f"[INFO] Config type: {type(config)}")
        print(f"[INFO] Has trading config: {hasattr(config, 'trading')}")
        print(f"[INFO] Has database config: {hasattr(config, 'database')}")
        
        # Test trading config specifically
        if hasattr(config, 'trading'):
            print(f"[INFO] Trading mode: {config.trading.mode}")
            print(f"[INFO] Primary currency: {config.trading.primary_currency}")
            print(f"[INFO] BTC allocation: {config.trading.btc_allocation}")
        
        return True
        
    except Exception as e:
        print(f"[ERROR] Configuration loading failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Main execution"""
    print("=" * 60)
    print("COMPREHENSIVE CONFIGURATION FIX")
    print("=" * 60)
    
    # Add flexible init to all dataclasses
    if not add_flexible_init_to_dataclasses():
        print("[CRITICAL] Failed to add flexible initialization!")
        sys.exit(1)
    
    # Test all configuration loading
    if not test_all_config_loading():
        print("[CRITICAL] Configuration still has issues!")
        sys.exit(1)
    
    print("\n" + "=" * 60)
    print("[SUCCESS] ALL CONFIGURATION CLASSES FIXED")
    print("All dataclass configs now handle any parameters!")
    print("=" * 60)

if __name__ == "__main__":
    main()
