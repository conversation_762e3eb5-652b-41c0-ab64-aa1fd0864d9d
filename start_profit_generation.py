#!/usr/bin/env python3
"""
IMMEDIATE PROFIT GENERATION LAUNCHER
Starts the unified system for maximum BTC profit generation
"""

import os
import sys
import asyncio
import subprocess
from pathlib import Path

def main():
    """Start profit generation immediately"""
    print("[PROFIT] Starting maximum profit generation with BTC priority...")
    
    # Set environment variables for maximum profit
    os.environ['FORCE_PROFIT_GENERATION'] = 'true'
    os.environ['BTC_PRIORITY'] = 'maximum'
    os.environ['AGGRESSIVE_MODE'] = 'true'
    os.environ['LIVE_TRADING'] = 'true'
    
    try:
        # Import and start the unified system
        from main_unified_system import start_unified_system
        
        print("[PROFIT] Launching unified system for BTC profit generation...")
        asyncio.run(start_unified_system())
        
    except Exception as e:
        print(f"[ERROR] Failed to start profit generation: {e}")
        print("[FALLBACK] Attempting direct system start...")
        
        # Fallback: direct subprocess
        cmd = [sys.executable, "main_unified_system.py"]
        subprocess.run(cmd, cwd=Path(__file__).parent)

if __name__ == "__main__":
    main()
