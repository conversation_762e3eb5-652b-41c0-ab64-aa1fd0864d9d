#!/usr/bin/env python3
"""
HTTPS MOBILE APP SERVER - SSL Enabled
This creates an HTTPS version of the mobile app server for secure mobile access
"""

from fastapi import FastAPI
from fastapi.responses import HTMLResponse, JSONResponse
import uvicorn
import os
from pathlib import Path

# Create FastAPI app for HTTPS mobile server
https_mobile_app = FastAPI(
    title="Bybit Trading Bot - HTTPS Mobile App Server",
    description="Secure HTTPS mobile app server with SSL certificate",
    version="1.0.0"
)

# Get current directory
current_dir = Path(__file__).parent

def load_mobile_app_html() -> str:
    """Load the mobile control app HTML"""
    try:
        with open(current_dir / "mobile_control_app.html", "r", encoding="utf-8") as f:
            return f.read()
    except FileNotFoundError:
        return """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="theme-color" content="#000000">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <title>Mobile Trading App - HTTPS</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
            background: linear-gradient(135deg, #0f0f23, #1a1a2e, #16213e);
            color: #fff; min-height: 100vh; display: flex; align-items: center; justify-content: center;
        }
        .container { text-align: center; padding: 40px; max-width: 500px; }
        h1 { color: #00ff88; font-size: 2rem; margin-bottom: 20px; }
        .secure-badge { background: #00ff88; color: #000; padding: 5px 15px; border-radius: 20px; font-size: 12px; font-weight: 700; margin-bottom: 20px; display: inline-block; }
        .btn { 
            background: linear-gradient(45deg, #00ff88, #00ccff); 
            color: #000; padding: 20px 30px; border: none; border-radius: 25px;
            font-weight: 700; text-decoration: none; display: block; margin: 20px 0;
            font-size: 18px; text-transform: uppercase;
        }
        .controls { display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin: 30px 0; }
        .control-btn { 
            padding: 15px; border: none; border-radius: 15px; font-weight: 700; 
            font-size: 14px; cursor: pointer; text-transform: uppercase;
        }
        .start { background: linear-gradient(45deg, #00ff88, #00cc77); color: #000; }
        .stop { background: linear-gradient(45deg, #ff4444, #cc3333); color: #fff; }
        .restart { background: linear-gradient(45deg, #ffaa00, #ff8800); color: #000; grid-column: 1 / -1; }
        .emergency { background: linear-gradient(45deg, #ff0066, #cc0044); color: #fff; grid-column: 1 / -1; }
    </style>
</head>
<body>
    <div class="container">
        <div class="secure-badge">🔒 SECURE HTTPS</div>
        <h1>📱 Mobile Trading Control</h1>
        <p style="color: #ccc; margin-bottom: 30px;">Secure mobile access to your trading system</p>
        
        <div class="controls">
            <button class="control-btn start" onclick="controlSystem('start')">🚀 START</button>
            <button class="control-btn stop" onclick="controlSystem('stop')">⏹️ STOP</button>
            <button class="control-btn restart" onclick="controlSystem('restart')">🔄 RESTART</button>
            <button class="control-btn emergency" onclick="controlSystem('emergency')">🚨 EMERGENCY</button>
        </div>
        
        <div style="background: rgba(255,255,255,0.1); padding: 20px; border-radius: 15px; margin: 20px 0;">
            <h3 style="color: #00ff88; margin-bottom: 10px;">System Status</h3>
            <p>Mobile Server: <span style="color: #00ff88;">Active</span></p>
            <p>Connection: <span style="color: #00ff88;">Secure HTTPS</span></p>
        </div>
        
        <a href="/mobile" class="btn">📱 Full Mobile Interface</a>
    </div>
    
    <script>
        async function controlSystem(action) {
            const endpoints = {
                'start': '/system/start',
                'stop': '/system/stop', 
                'restart': '/system/restart',
                'emergency': '/api/trading/emergency-stop'
            };
            
            try {
                const response = await fetch(endpoints[action], { method: 'POST' });
                const result = await response.json();
                alert(result.message || `${action} command sent`);
            } catch (error) {
                alert(`Failed to ${action} system: ${error.message}`);
            }
        }
        
        // Add to home screen helper
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.register('/service-worker.js');
        }
    </script>
</body>
</html>
        """

# Root endpoint
@https_mobile_app.get("/", response_class=HTMLResponse)
async def root():
    """Root endpoint - HTTPS mobile app"""
    return HTMLResponse(content="""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="theme-color" content="#000000">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <title>Bybit Trading Bot - Secure Mobile Access</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
            background: linear-gradient(135deg, #0f0f23, #1a1a2e, #16213e);
            color: #fff; min-height: 100vh; display: flex; align-items: center; justify-content: center;
        }
        .container { text-align: center; padding: 40px; max-width: 500px; }
        h1 { color: #00ff88; font-size: 2.5rem; margin-bottom: 20px; }
        .secure-badge { 
            background: linear-gradient(45deg, #00ff88, #00ccff); 
            color: #000; padding: 8px 20px; border-radius: 25px; 
            font-size: 14px; font-weight: 700; margin-bottom: 20px; 
            display: inline-block; box-shadow: 0 4px 15px rgba(0, 255, 136, 0.3);
        }
        .subtitle { color: #ccc; margin-bottom: 40px; font-size: 1.2rem; }
        .btn-grid { display: grid; gap: 20px; margin: 30px 0; }
        .btn { 
            background: linear-gradient(45deg, #00ff88, #00ccff); 
            color: #000; padding: 20px 30px; border: none; border-radius: 25px;
            font-weight: 700; text-decoration: none; display: block;
            font-size: 18px; text-transform: uppercase; letter-spacing: 1px;
            transition: all 0.3s ease; cursor: pointer;
        }
        .btn:hover { transform: translateY(-2px); box-shadow: 0 10px 20px rgba(0, 255, 136, 0.3); }
        .btn.secondary { background: linear-gradient(45deg, #ffaa00, #ff8800); }
        .status { 
            background: rgba(255, 255, 255, 0.1); border-radius: 15px; padding: 20px; 
            margin: 30px 0; border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .status-indicator { 
            display: inline-block; width: 12px; height: 12px; 
            border-radius: 50%; margin-right: 10px;
            background: #00ff88; box-shadow: 0 0 10px rgba(0, 255, 136, 0.5);
        }
        .security-info {
            background: rgba(0, 255, 136, 0.1); border: 1px solid rgba(0, 255, 136, 0.3);
            border-radius: 15px; padding: 20px; margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="secure-badge">🔒 SECURE HTTPS CONNECTION</div>
        <h1>📱 Secure Mobile Control</h1>
        <p class="subtitle">HTTPS Mobile App Server</p>
        
        <div class="status">
            <span class="status-indicator"></span>
            Secure Mobile Server: Active
        </div>
        
        <div class="security-info">
            <h3 style="color: #00ff88; margin-bottom: 10px;">🔒 Security Features</h3>
            <ul style="text-align: left; color: #ccc;">
                <li>✅ HTTPS/SSL encryption</li>
                <li>✅ Secure mobile access</li>
                <li>✅ Browser compatibility</li>
                <li>✅ PWA installation ready</li>
            </ul>
        </div>
        
        <div class="btn-grid">
            <a href="/mobile" class="btn">📱 Launch Mobile App</a>
            <a href="/control" class="btn">🎮 Control Panel</a>
            <a href="/install-guide" class="btn secondary">📋 Installation Guide</a>
        </div>
        
        <p style="color: #888; margin-top: 30px; font-size: 14px;">
            Secure HTTPS connection ensures your trading data is encrypted.<br>
            Compatible with all mobile browsers including DuckDuckGo.
        </p>
    </div>
</body>
</html>
    """)

# Mobile app endpoints (same as HTTP version)
@https_mobile_app.get("/mobile", response_class=HTMLResponse)
async def mobile_control_app():
    """Serve the mobile control app via HTTPS"""
    return HTMLResponse(content=load_mobile_app_html())

@https_mobile_app.get("/control", response_class=HTMLResponse)
async def control_panel():
    """Serve the control panel via HTTPS"""
    return HTMLResponse(content=load_mobile_app_html())

# Status and API endpoints
@https_mobile_app.get("/status")
async def get_status():
    """Get HTTPS mobile server status"""
    return {
        "mobile_server": "active",
        "connection": "https_secure",
        "ssl_enabled": True,
        "trading_system": "unknown", 
        "message": "Secure HTTPS mobile app server running",
        "timestamp": "2025-07-17T10:30:00Z",
        "security_features": ["https_encryption", "ssl_certificate", "secure_mobile_access"]
    }

@https_mobile_app.get("/api/dashboard")
async def get_dashboard():
    """Get dashboard data via HTTPS"""
    return {
        "status": "https_mobile_server",
        "message": "Secure connection established",
        "mobile_server": "active",
        "ssl_enabled": True,
        "features_available": [
            "Secure mobile app installation",
            "HTTPS control interface", 
            "SSL-encrypted communication",
            "Browser-compatible PWA"
        ]
    }

# System control endpoints
@https_mobile_app.post("/system/start")
async def start_system():
    """Start system endpoint via HTTPS"""
    return {
        "status": "https_mobile_server",
        "message": "Start command received via secure connection",
        "action": "start_requested",
        "ssl_secured": True
    }

@https_mobile_app.post("/system/stop")
async def stop_system():
    """Stop system endpoint via HTTPS"""
    return {
        "status": "https_mobile_server", 
        "message": "Stop command received via secure connection",
        "action": "stop_requested",
        "ssl_secured": True
    }

@https_mobile_app.post("/system/restart")
async def restart_system():
    """Restart system endpoint via HTTPS"""
    return {
        "status": "https_mobile_server",
        "message": "Restart command received via secure connection",
        "action": "restart_requested",
        "ssl_secured": True
    }

@https_mobile_app.post("/api/trading/emergency-stop")
async def emergency_stop():
    """Emergency stop endpoint via HTTPS"""
    return {
        "status": "https_mobile_server",
        "message": "Emergency stop command received via secure connection",
        "action": "emergency_stop_requested",
        "ssl_secured": True
    }

# PWA Manifest for HTTPS
@https_mobile_app.get("/manifest.json")
async def get_https_manifest():
    """PWA manifest for HTTPS mobile installation"""
    manifest = {
        "name": "Bybit Trading Bot (Secure)",
        "short_name": "Trading Bot HTTPS",
        "description": "Secure HTTPS mobile control for Bybit Trading Bot",
        "start_url": "/mobile",
        "display": "standalone",
        "background_color": "#0f0f23",
        "theme_color": "#00ff88",
        "orientation": "portrait",
        "scope": "/",
        "protocol_handlers": [
            {
                "protocol": "https",
                "url": "/mobile"
            }
        ],
        "icons": [
            {
                "src": "/static/icon-192x192.png",
                "sizes": "192x192",
                "type": "image/png",
                "purpose": "any maskable"
            },
            {
                "src": "/static/favicon.png",
                "sizes": "32x32", 
                "type": "image/png"
            },
            {
                "src": "/static/logo.svg",
                "sizes": "120x120",
                "type": "image/svg+xml"
            }
        ],
        "shortcuts": [
            {
                "name": "Secure Start",
                "url": "/mobile?action=start",
                "description": "Start trading system securely"
            },
            {
                "name": "Secure Stop", 
                "url": "/mobile?action=stop",
                "description": "Stop trading system securely"
            }
        ]
    }
    return JSONResponse(content=manifest)

def create_self_signed_cert():
    """Create a self-signed certificate for HTTPS"""
    try:
        from cryptography import x509
        from cryptography.x509.oid import NameOID
        from cryptography.hazmat.primitives import hashes, serialization
        from cryptography.hazmat.primitives.asymmetric import rsa
        import datetime
        
        # Generate private key
        private_key = rsa.generate_private_key(
            public_exponent=65537,
            key_size=2048,
        )
        
        # Create certificate
        subject = issuer = x509.Name([
            x509.NameAttribute(NameOID.COUNTRY_NAME, "US"),
            x509.NameAttribute(NameOID.STATE_OR_PROVINCE_NAME, "State"),
            x509.NameAttribute(NameOID.LOCALITY_NAME, "City"),
            x509.NameAttribute(NameOID.ORGANIZATION_NAME, "Bybit Trading Bot"),
            x509.NameAttribute(NameOID.COMMON_NAME, "localhost"),
        ])
        
        cert = x509.CertificateBuilder().subject_name(
            subject
        ).issuer_name(
            issuer
        ).public_key(
            private_key.public_key()
        ).serial_number(
            x509.random_serial_number()
        ).not_valid_before(
            datetime.datetime.utcnow()
        ).not_valid_after(
            datetime.datetime.utcnow() + datetime.timedelta(days=365)
        ).add_extension(
            x509.SubjectAlternativeName([
                x509.DNSName("localhost"),
                x509.DNSName("**************"),
                x509.IPAddress(ipaddress.IPv4Address("127.0.0.1")),
                x509.IPAddress(ipaddress.IPv4Address("**************")),
            ]),
            critical=False,
        ).sign(private_key, hashes.SHA256())
        
        # Save certificate and key
        with open("cert.pem", "wb") as f:
            f.write(cert.public_bytes(serialization.Encoding.PEM))
        
        with open("key.pem", "wb") as f:
            f.write(private_key.private_bytes(
                encoding=serialization.Encoding.PEM,
                format=serialization.PrivateFormat.PKCS8,
                encryption_algorithm=serialization.NoEncryption()
            ))
        
        print("[HTTPS] Self-signed certificate created successfully")
        return True
        
    except ImportError:
        print("[HTTPS] cryptography package not available - using HTTP only")
        return False
    except Exception as e:
        print(f"[HTTPS] Failed to create certificate: {e}")
        return False

def run_https_mobile_server(host: str = "0.0.0.0", port: int = 8443):
    """Run the HTTPS mobile app server"""
    print("=" * 60)
    print("HTTPS MOBILE APP SERVER")
    print("=" * 60)
    
    # Try to create SSL certificate
    ssl_available = create_self_signed_cert()
    
    if ssl_available and os.path.exists("cert.pem") and os.path.exists("key.pem"):
        print(f"[HTTPS] Starting secure server on https://{host}:{port}")
        print(f"[HTTPS] Mobile app: https://**************:{port}/mobile")
        print(f"[HTTPS] Control panel: https://**************:{port}/control")
        print("[HTTPS] SSL certificate: Self-signed (accept security warning)")
        print("[HTTPS] Compatible with DuckDuckGo and all mobile browsers")
        print("=" * 60)
        
        # Run with SSL
        uvicorn.run(
            https_mobile_app, 
            host=host, 
            port=port, 
            ssl_keyfile="key.pem",
            ssl_certfile="cert.pem",
            log_level="info"
        )
    else:
        print("[HTTPS] SSL not available - falling back to HTTP")
        print(f"[HTTP] Starting server on http://{host}:8000")
        uvicorn.run(https_mobile_app, host=host, port=8000, log_level="info")

if __name__ == "__main__":
    """
    HTTPS MOBILE APP SERVER
    - Creates self-signed SSL certificate
    - Runs secure HTTPS server
    - Compatible with DuckDuckGo browser
    - Falls back to HTTP if SSL unavailable
    """
    import ipaddress
    run_https_mobile_server()
