<?xml version="1.0" encoding="UTF-8"?>
<svg width="120" height="120" viewBox="0 0 120 120" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <radialGradient id="logoGradient" cx="50%" cy="30%" r="70%">
      <stop offset="0%" style="stop-color:#ffffff;stop-opacity:0.9"/>
      <stop offset="100%" style="stop-color:#000000;stop-opacity:1"/>
    </radialGradient>
  </defs>
  
  <!-- Background circle -->
  <circle cx="60" cy="60" r="58" fill="url(#logoGradient)" stroke="#2d3748" stroke-width="2"/>
  
  <!-- Stylized profile silhouette -->
  <path d="M35 40 Q45 25 60 25 Q75 25 85 40 Q88 50 85 65 Q80 75 70 80 Q60 85 50 80 Q40 75 35 65 Q32 50 35 40 Z" 
        fill="#000000" opacity="0.8"/>
  
  <!-- Hair detail -->
  <path d="M40 35 Q50 20 60 22 Q70 20 80 35 Q85 40 82 45 Q75 30 60 28 Q45 30 38 45 Q35 40 40 35 Z" 
        fill="#1a1a1a" opacity="0.9"/>
  
  <!-- Face profile -->
  <path d="M55 35 Q65 35 70 45 Q72 55 70 65 Q65 70 60 70 Q55 70 52 65 Q50 55 52 45 Q55 35 55 35 Z" 
        fill="#2d3748" opacity="0.7"/>
  
  <!-- Trading symbol overlay -->
  <text x="60" y="95" font-family="Arial, sans-serif" font-size="12" font-weight="bold" 
        text-anchor="middle" fill="#00d4aa">$</text>
  
  <!-- Border accent -->
  <circle cx="60" cy="60" r="58" fill="none" stroke="#00d4aa" stroke-width="1" opacity="0.5"/>
</svg>
