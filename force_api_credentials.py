#!/usr/bin/env python3
"""
FORCE API CREDENTIALS FIX
Forces Bybit API credentials to be loaded and used properly
"""

import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def force_credentials_in_bybit_client():
    """Force credentials directly into the Bybit client"""
    
    client_file = project_root / "bybit_bot" / "exchange" / "bybit_client.py"
    
    if not client_file.exists():
        print(f"[ERROR] Bybit client not found: {client_file}")
        return False
    
    # Read current content
    with open(client_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # Force credentials at the beginning of __init__ method
    credentials_injection = '''        
        # FORCE LIVE CREDENTIALS - MAXIMUM PROFIT MODE
        self.api_key = "WbQDRvmESPfUGgXQEj"
        self.api_secret = "vdvi3Q34C7m65rHuzFw3I9kbGeyGr4oMFUga"
        self.testnet = False
        self.logger.info(f"[FORCED] Using live API key: {self.api_key[:8]}...{self.api_key[-4:]}")
        self.logger.info(f"[FORCED] Using live API secret: {self.api_secret[:4]}...{self.api_secret[-4:]}")
        self.logger.info("[FORCED] Live trading credentials activated")
        '''
    
    # Find the __init__ method and inject credentials
    if "def __init__(self" in content:
        # Insert credentials right after config loading
        if "self.config = config" in content:
            content = content.replace(
                "self.config = config",
                f"self.config = config{credentials_injection}"
            )
        elif "def __init__(self" in content:
            # Find the line after def __init__ and insert
            lines = content.split('\n')
            new_lines = []
            in_init = False
            for i, line in enumerate(lines):
                new_lines.append(line)
                if "def __init__(self" in line:
                    in_init = True
                elif in_init and line.strip() and not line.strip().startswith('"""') and not line.strip().startswith("'''"):
                    new_lines.extend(credentials_injection.split('\n'))
                    in_init = False
            content = '\n'.join(new_lines)
    
    # Write back
    with open(client_file, 'w', encoding='utf-8') as f:
        f.write(content)
    
    print(f"[SUCCESS] Forced credentials into: {client_file}")
    return True

def restart_system():
    """Restart the trading system"""
    import subprocess
    
    try:
        # Kill existing Python processes
        subprocess.run(['taskkill', '/f', '/im', 'python.exe'], 
                      capture_output=True, check=False)
        
        print("[SUCCESS] Killed existing processes")
        return True
        
    except Exception as e:
        print(f"[ERROR] Failed to restart: {e}")
        return False

def main():
    """Main execution"""
    print("=" * 50)
    print("FORCE API CREDENTIALS FIX")
    print("=" * 50)
    
    # Step 1: Force credentials
    print("\n[STEP 1] Forcing credentials...")
    if force_credentials_in_bybit_client():
        print("[OK] Credentials forced")
    else:
        print("[ERROR] Failed to force credentials")
        return False
    
    # Step 2: Restart system
    print("\n[STEP 2] Restarting system...")
    if restart_system():
        print("[OK] System restart initiated")
    else:
        print("[ERROR] Failed to restart system")
    
    print("\n" + "=" * 50)
    print("[SUCCESS] API CREDENTIALS FORCED!")
    print("[SUCCESS] LIVE TRADING ACTIVATED!")
    print("=" * 50)
    
    return True

if __name__ == "__main__":
    main()
