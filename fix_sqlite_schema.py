#!/usr/bin/env python3
"""
SQLite Database Schema Fix Script
Creates/fixes database schema for immediate profit generation
"""
import sys
import logging
import sqlite3
from pathlib import Path
import base64
import hashlib

# Add parent directory to path
sys.path.append(str(Path(__file__).parent))

from config_manager import ConfigManager, Environment

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(message)s')
logger = logging.getLogger(__name__)


def fix_sqlite_schema():
    """Fix all SQLite database schema issues for maximum profit generation"""
    
    print("=" * 80)
    print("BYBIT TRADING BOT - SQLITE SCHEMA FIX")
    print("=" * 80)
    
    try:
        # Generate the same Fernet key as setup
        password = 'SuperGPT_AES256_Trading_Bot_2025_MAX_PROFIT_ENCRYPTION_KEY_SECURE'
        key = base64.urlsafe_b64encode(hashlib.sha256(password.encode()).digest())
        encryption_key = key.decode()
        
        # Initialize ConfigManager
        config_manager = ConfigManager(
            config_dir="config",
            environment=Environment.PRODUCTION,
            encryption_key=encryption_key
        )
        
        logger.info("[INFO] Connecting to SQLite database...")
        
        # Connect to SQLite
        conn = sqlite3.connect('bybit_trading_bot.db')
        cursor = conn.cursor()
        
        logger.info("[OK] Connected to SQLite database successfully")
        
        # Create all required tables with proper schema
        logger.info("[INFO] Creating/updating database tables...")
        
        # Positions table with metadata column
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS positions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                side TEXT NOT NULL,
                size REAL NOT NULL,
                entry_price REAL,
                current_price REAL,
                unrealized_pnl REAL DEFAULT 0,
                realized_pnl REAL DEFAULT 0,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                status TEXT DEFAULT 'active',
                metadata TEXT DEFAULT '{}',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Check if metadata column exists, add if missing
        cursor.execute("PRAGMA table_info(positions)")
        columns = [column[1] for column in cursor.fetchall()]
        
        if 'metadata' not in columns:
            logger.info("[INFO] Adding metadata column to positions table...")
            cursor.execute("ALTER TABLE positions ADD COLUMN metadata TEXT DEFAULT '{}'")
            logger.info("[OK] Metadata column added successfully!")
        else:
            logger.info("[OK] Metadata column already exists!")
        
        # Trades table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS trades (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                position_id INTEGER,
                symbol TEXT NOT NULL,
                side TEXT NOT NULL,
                quantity REAL NOT NULL,
                price REAL NOT NULL,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                commission REAL DEFAULT 0,
                realized_pnl REAL DEFAULT 0,
                trade_type TEXT DEFAULT 'market',
                order_id TEXT,
                metadata TEXT DEFAULT '{}',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (position_id) REFERENCES positions (id) ON DELETE CASCADE
            )
        ''')
        
        # Market data table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS market_data (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                symbol TEXT NOT NULL,
                timestamp DATETIME NOT NULL,
                open_price REAL,
                high_price REAL,
                low_price REAL,
                close_price REAL,
                volume REAL,
                turnover REAL,
                interval_type TEXT,
                metadata TEXT DEFAULT '{}',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Strategy performance table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS strategy_performance (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                strategy_name TEXT NOT NULL,
                symbol TEXT NOT NULL,
                profit_loss REAL DEFAULT 0,
                win_rate REAL DEFAULT 0,
                trades_count INTEGER DEFAULT 0,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                metadata TEXT DEFAULT '{}',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # System metrics table
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS system_metrics (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                metric_name TEXT NOT NULL,
                metric_value REAL NOT NULL,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
                metadata TEXT DEFAULT '{}',
                created_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        
        # Create performance indexes
        logger.info("[INFO] Creating performance indexes...")
        
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_positions_symbol ON positions(symbol)",
            "CREATE INDEX IF NOT EXISTS idx_positions_created_at ON positions(created_at DESC)",
            "CREATE INDEX IF NOT EXISTS idx_trades_position_id ON trades(position_id)",
            "CREATE INDEX IF NOT EXISTS idx_trades_created_at ON trades(created_at DESC)",
            "CREATE INDEX IF NOT EXISTS idx_market_data_symbol_created_at ON market_data(symbol, created_at DESC)",
            "CREATE INDEX IF NOT EXISTS idx_strategy_performance_name ON strategy_performance(strategy_name)",
            "CREATE INDEX IF NOT EXISTS idx_system_metrics_name ON system_metrics(metric_name)"
        ]
        
        for index_sql in indexes:
            cursor.execute(index_sql)
        
        logger.info("[OK] All performance indexes created")
        
        # Commit changes
        conn.commit()
        
        # Verify tables exist
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = [table[0] for table in cursor.fetchall()]
        
        logger.info(f"[INFO] Database contains {len(tables)} tables: {', '.join(tables)}")
        
        cursor.close()
        conn.close()
        
        print("\n" + "=" * 80)
        print("✅ SUCCESS: SQLITE DATABASE SCHEMA FULLY FIXED")
        print("✅ ALL CRITICAL TABLES CREATED/VERIFIED")
        print("✅ METADATA COLUMNS ADDED")
        print("✅ PERFORMANCE INDEXES CREATED")
        print("✅ SYSTEM READY FOR MAXIMUM PROFIT GENERATION")
        print("=" * 80)
        
        return True
        
    except Exception as e:
        logger.error(f"[ERROR] SQLite schema fix failed: {e}")
        print("\n" + "=" * 80)
        print("❌ FAILED: SQLITE SCHEMA FIX")
        print(f"❌ ERROR: {e}")
        print("=" * 80)
        return False


if __name__ == "__main__":
    fix_sqlite_schema()
