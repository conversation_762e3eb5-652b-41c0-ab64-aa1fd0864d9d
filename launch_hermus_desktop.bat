@echo off
echo ========================================
echo HERMUS Trading Bot - Desktop Launch
echo ========================================
echo.

REM Change to E: drive and project directory
E:
cd /d "E:\The_real_deal_copy\Bybit_Bot\BOT"

echo [1/3] Activating bybit-trader conda environment...
call conda activate bybit-trader
if errorlevel 1 (
    echo ERROR: Failed to activate bybit-trader environment
    echo Please ensure conda is installed and bybit-trader environment exists
    pause
    exit /b 1
)

echo [2/3] Checking desktop application...
if not exist "desktop-app\package.json" (
    echo ERROR: Desktop application not found
    echo Please run setup_complete_desktop.bat first
    pause
    exit /b 1
)

echo [3/3] Starting HERMUS Desktop Application...
cd desktop-app

REM Check if built files exist
if not exist "dist\index.html" (
    echo Building application first...
    call npm run build
    if errorlevel 1 (
        echo ERROR: Failed to build application
        pause
        exit /b 1
    )
)

echo.
echo ========================================
echo HERMUS Desktop Application Starting...
echo ========================================
echo.
echo Features:
echo - System Tray Integration
echo - Real-time Trading Controls
echo - Desktop Notifications
echo - Process Management
echo - Live Trading Dashboard
echo.
echo The application will appear in your system tray.
echo Right-click the tray icon for options.
echo.

REM Start Electron application
call npm run electron

echo.
echo Desktop application closed.
pause
