#!/usr/bin/env python3
"""
Fix Database Columns
Adds missing columns to existing database tables
"""

import asyncio
import sys
from pathlib import Path

# Add the current directory to Python path
sys.path.insert(0, str(Path(__file__).parent))

from bybit_bot.database.connection import DatabaseManager
from bybit_bot.core.config import EnhancedBotConfig
from bybit_bot.core.logger import TradingBotLogger

async def fix_database_columns():
    """Fix missing database columns"""
    logger = TradingBotLogger("DatabaseColumnFix")
    
    try:
        # Initialize configuration and database
        config = EnhancedBotConfig()
        db_manager = DatabaseManager(config)
        await db_manager.initialize()
        
        logger.info("Checking and fixing database columns...")
        
        # List of columns to add to positions table if missing
        positions_columns = [
            ("strategy", "VARCHAR(50)"),
            ("extra_data", "TEXT"),
            ("stop_loss", "REAL"),
            ("take_profit", "REAL"),
        ]
        
        # Check and add missing columns to positions table
        for column_name, column_type in positions_columns:
            try:
                # Check if column exists
                check_query = """
                PRAGMA table_info(positions)
                """
                result = await db_manager.execute_sql_with_result(check_query)
                columns = [row[1] for row in result] if result else []
                
                if column_name not in columns:
                    # Add missing column
                    add_column_query = f"""
                    ALTER TABLE positions ADD COLUMN {column_name} {column_type}
                    """
                    await db_manager.execute_sql(add_column_query)
                    logger.info(f"✅ Added missing column: positions.{column_name}")
                else:
                    logger.info(f"✅ Column exists: positions.{column_name}")
                    
            except Exception as e:
                logger.error(f"Error adding column {column_name}: {e}")
        
        # Create index on strategy column if it doesn't exist
        try:
            create_index_query = """
            CREATE INDEX IF NOT EXISTS idx_positions_strategy_status ON positions(strategy, status)
            """
            await db_manager.execute_sql(create_index_query)
            logger.info("✅ Created strategy index on positions table")
        except Exception as e:
            logger.error(f"Error creating index: {e}")
        
        # Verify the fix
        verify_query = """
        SELECT symbol, strategy, status, created_at 
        FROM positions 
        LIMIT 1
        """
        try:
            result = await db_manager.execute_sql_with_result(verify_query)
            logger.info("✅ Database column fix verified successfully")
        except Exception as e:
            logger.error(f"Verification failed: {e}")
        
        await db_manager.close()
        logger.info("✅ Database column fix completed successfully")
        return True
        
    except Exception as e:
        logger.error(f"Failed to fix database columns: {e}")
        return False

async def main():
    """Main execution function"""
    print("🔧 Fixing Database Columns...")
    success = await fix_database_columns()
    
    if success:
        print("✅ Database columns fixed successfully!")
        return 0
    else:
        print("❌ Failed to fix database columns")
        return 1

if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
