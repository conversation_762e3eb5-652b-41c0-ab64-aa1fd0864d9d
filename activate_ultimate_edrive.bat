@echo off
REM ============================================================================
REM ULTIMATE E-DRIVE CONDA ACTIVATION AND SYSTEM SETUP
REM ============================================================================
REM This script completely sets up conda and Python on E drive ONLY
REM ============================================================================

echo.
echo ================================================================================
echo ULTIMATE E-DRIVE CONDA ACTIVATION
echo ================================================================================

REM Change to project directory first
cd /d "E:\The_real_deal_copy\Bybit_Bot\BOT"

REM Set conda paths explicitly
set "CONDA_ROOT=E:\conda\miniconda3"
set "CONDA_SCRIPTS=%CONDA_ROOT%\Scripts"
set "CONDA_CONDABIN=%CONDA_ROOT%\condabin"
set "CONDA_ENV_PATH=E:\conda\envs\bybit-trader"

REM Update PATH to prioritize E drive conda
set "PATH=%CONDA_ENV_PATH%;%CONDA_ENV_PATH%\Scripts;%CONDA_ENV_PATH%\Library\bin;%CONDA_SCRIPTS%;%CONDA_CONDABIN%;%PATH%"

REM Set conda environment variables
set "CONDA_PREFIX=%CONDA_ENV_PATH%"
set "CONDA_DEFAULT_ENV=bybit-trader"
set "CONDA_PYTHON_EXE=%CONDA_ENV_PATH%\python.exe"

REM Set project environment variables
set "PYTHONPATH=E:\The_real_deal_copy\Bybit_Bot\BOT"
set "BYBIT_BOT_HOME=E:\The_real_deal_copy\Bybit_Bot\BOT"

REM Initialize conda for this session if not already done
if not exist "%USERPROFILE%\.condarc" (
    echo [INFO] Initializing conda for user...
    "%CONDA_SCRIPTS%\conda.exe" init cmd.exe >nul 2>&1
)

REM Activate the specific environment
echo [INFO] Activating bybit-trader environment...
call "%CONDA_CONDABIN%\conda.bat" activate "%CONDA_ENV_PATH%"

REM Verify activation
echo.
echo [VERIFICATION] Environment Status:
echo Python Executable: %CONDA_PYTHON_EXE%
echo Conda Environment: %CONDA_DEFAULT_ENV%
echo Project Directory: %BYBIT_BOT_HOME%
echo Current Directory: %CD%

REM Test Python
echo.
echo [TEST] Python Version:
python --version

echo.
echo [SUCCESS] E-Drive Conda Environment Ready!
echo ================================================================================
