<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>📱 Trading Bot QR Codes</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
            color: #ffffff;
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1000px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
        }

        .header h1 {
            font-size: 32px;
            margin-bottom: 12px;
            background: linear-gradient(135deg, #00ff88 0%, #00cc6a 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .qr-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 30px;
            margin-bottom: 40px;
        }

        .qr-card {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 20px;
            padding: 30px;
            text-align: center;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }

        .qr-card:hover {
            transform: translateY(-5px);
            border-color: #00ff88;
            box-shadow: 0 20px 40px rgba(0, 255, 136, 0.1);
        }

        .qr-title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 16px;
            color: #00ff88;
        }

        .qr-placeholder {
            width: 200px;
            height: 200px;
            background: #ffffff;
            margin: 0 auto 20px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }

        .qr-url {
            font-family: monospace;
            background: #000;
            padding: 12px;
            border-radius: 8px;
            margin: 16px 0;
            font-size: 12px;
            word-break: break-all;
            color: #00ff88;
        }

        .qr-description {
            color: #c0c0c0;
            font-size: 14px;
            line-height: 1.5;
        }

        .instructions {
            background: rgba(0, 255, 136, 0.1);
            border: 1px solid #00ff88;
            border-radius: 15px;
            padding: 30px;
            margin-top: 40px;
        }

        .instructions h2 {
            color: #00ff88;
            margin-bottom: 20px;
        }

        .step-list {
            list-style: none;
            margin-left: 0;
        }

        .step-list li {
            display: flex;
            align-items: flex-start;
            margin-bottom: 12px;
            font-size: 15px;
        }

        .step-number {
            background: #00ff88;
            color: #000;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 12px;
            margin-right: 12px;
            flex-shrink: 0;
        }

        .copy-button {
            background: linear-gradient(135deg, #00ff88 0%, #00cc6a 100%);
            color: #000;
            border: none;
            padding: 8px 16px;
            border-radius: 8px;
            font-weight: 600;
            font-size: 12px;
            cursor: pointer;
            margin-top: 8px;
        }

        .copy-button:hover {
            transform: translateY(-1px);
        }

        @media (max-width: 768px) {
            .qr-grid {
                grid-template-columns: 1fr;
            }
            
            .qr-placeholder {
                width: 150px;
                height: 150px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📱 Bybit Trading Bot - QR Codes</h1>
            <p style="color: #a0a0a0; font-size: 16px;">Scan any QR code with your phone's camera for instant access</p>
        </div>

        <div class="qr-grid">
            <!-- Main Trading Bot QR -->
            <div class="qr-card">
                <h3 class="qr-title">🚀 Main Trading Dashboard</h3>
                <div class="qr-placeholder" id="main-qr">
                    <div style="text-align: center;">
                        <div style="font-size: 48px; margin-bottom: 8px;">📊</div>
                        <div style="color: #666; font-size: 12px;">QR Code</div>
                    </div>
                </div>
                <div class="qr-url" id="main-url">http://*************:8000</div>
                <button class="copy-button" onclick="copyUrl('main-url')">Copy URL</button>
                <p class="qr-description">
                    Scan to access the main trading dashboard with real-time monitoring and controls
                </p>
            </div>

            <!-- Installation Guide QR -->
            <div class="qr-card">
                <h3 class="qr-title">📲 Installation Guide</h3>
                <div class="qr-placeholder" id="install-qr">
                    <div style="text-align: center;">
                        <div style="font-size: 48px; margin-bottom: 8px;">⬇️</div>
                        <div style="color: #666; font-size: 12px;">QR Code</div>
                    </div>
                </div>
                <div class="qr-url" id="install-url">http://*************:8000/mobile-install</div>
                <button class="copy-button" onclick="copyUrl('install-url')">Copy URL</button>
                <p class="qr-description">
                    Scan for detailed installation instructions and device-specific setup guides
                </p>
            </div>

            <!-- Emergency Stop QR -->
            <div class="qr-card">
                <h3 class="qr-title" style="color: #ff4444;">🚨 Emergency Stop</h3>
                <div class="qr-placeholder" id="emergency-qr">
                    <div style="text-align: center;">
                        <div style="font-size: 48px; margin-bottom: 8px;">🛑</div>
                        <div style="color: #666; font-size: 12px;">QR Code</div>
                    </div>
                </div>
                <div class="qr-url" id="emergency-url">http://*************:8000/?action=stop</div>
                <button class="copy-button" onclick="copyUrl('emergency-url')">Copy URL</button>
                <p class="qr-description">
                    Emergency access to immediately stop all trading activities
                </p>
            </div>
        </div>

        <div class="instructions">
            <h2>📱 How to Scan QR Codes</h2>
            <ol class="step-list">
                <li>
                    <span class="step-number">1</span>
                    Open your phone's camera app or QR code scanner
                </li>
                <li>
                    <span class="step-number">2</span>
                    Point the camera at any QR code above
                </li>
                <li>
                    <span class="step-number">3</span>
                    Tap the notification that appears
                </li>
                <li>
                    <span class="step-number">4</span>
                    Your trading bot opens instantly!
                </li>
            </ol>

            <div style="margin-top: 20px; padding: 20px; background: rgba(0, 0, 0, 0.3); border-radius: 10px;">
                <h3 style="color: #00ff88; margin-bottom: 12px;">💡 Pro Tips:</h3>
                <ul style="color: #c0c0c0; margin-left: 20px;">
                    <li>📱 For best experience, install as PWA (Progressive Web App)</li>
                    <li>🔖 Bookmark the main URL for quick access</li>
                    <li>🚨 Save emergency stop link for critical situations</li>
                    <li>📶 Ensure your phone is on the same WiFi network</li>
                </ul>
            </div>
        </div>

        <div style="text-align: center; margin-top: 40px; color: #666;">
            <p>🔧 To generate actual QR codes, install the qrcode library:</p>
            <div style="font-family: monospace; background: #000; padding: 12px; border-radius: 8px; margin: 10px 0; color: #00ff88;">
                pip install qrcode[pil]
            </div>
            <p>Then run: <code style="background: #333; padding: 4px 8px; border-radius: 4px;">python generate_qr_codes.py</code></p>
        </div>
    </div>

    <script>
        // Copy URL function
        function copyUrl(elementId) {
            const element = document.getElementById(elementId);
            const text = element.textContent;
            
            navigator.clipboard.writeText(text).then(() => {
                // Show success feedback
                const button = event.target;
                const originalText = button.textContent;
                button.textContent = 'Copied!';
                button.style.background = '#00ff88';
                
                setTimeout(() => {
                    button.textContent = originalText;
                    button.style.background = 'linear-gradient(135deg, #00ff88 0%, #00cc6a 100%)';
                }, 2000);
            }).catch(() => {
                // Fallback for older browsers
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);
                
                alert('URL copied to clipboard!');
            });
        }

        // Generate QR codes using online service (for demonstration)
        function generateQRCode(text, elementId) {
            const qrElement = document.getElementById(elementId);
            const size = 200;
            const qrUrl = `https://api.qrserver.com/v1/create-qr-code/?size=${size}x${size}&data=${encodeURIComponent(text)}&color=000000&bgcolor=ffffff&format=png&ecc=M`;
            
            qrElement.innerHTML = `<img src="${qrUrl}" alt="QR Code" style="width: 100%; height: 100%; object-fit: contain; border-radius: 8px;">`;
        }

        // Initialize QR codes
        window.addEventListener('load', () => {
            generateQRCode('http://*************:8000', 'main-qr');
            generateQRCode('http://*************:8000/mobile-install', 'install-qr');
            generateQRCode('http://*************:8000/?action=stop', 'emergency-qr');
        });

        // Check if accessed from mobile device
        if (/Android|iPhone|iPad|iPod/i.test(navigator.userAgent)) {
            document.body.insertAdjacentHTML('afterbegin', `
                <div style="background: linear-gradient(135deg, #00ff88 0%, #00cc6a 100%); color: #000; padding: 16px; text-align: center; font-weight: 600; margin-bottom: 20px; border-radius: 12px;">
                    🎉 You're on mobile! <a href="http://*************:8000" style="color: #000; text-decoration: underline;">→ Open Trading Bot Now</a>
                </div>
            `);
        }
    </script>
</body>
</html>
