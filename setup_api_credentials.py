#!/usr/bin/env python3
"""
SECURE API CREDENTIALS SETUP
Sets up Bybit API credentials securely for live trading
"""

import os
import getpass
from pathlib import Path

def setup_api_credentials():
    """Setup Bybit API credentials securely"""
    print("=" * 60)
    print("🔐 SECURE BYBIT API CREDENTIALS SETUP")
    print("=" * 60)
    print()
    print("To enable live trading with your Bybit account:")
    print("1. Go to https://www.bybit.com/app/user/api-management")
    print("2. Create a new API key with trading permissions")
    print("3. Enter the credentials below (they will be stored securely)")
    print()
    
    # Get API credentials securely
    api_key = getpass.getpass("Enter your Bybit API Key: ").strip()
    api_secret = getpass.getpass("Enter your Bybit API Secret: ").strip()
    
    if not api_key or not api_secret:
        print("❌ API credentials cannot be empty")
        return False
    
    # Test credentials format
    if len(api_key) < 10 or len(api_secret) < 10:
        print("❌ API credentials appear to be too short")
        return False
    
    # Create secure environment file
    env_content = f"""# BYBIT API CREDENTIALS - KEEP SECURE
BYBIT_API_KEY={api_key}
BYBIT_API_SECRET={api_secret}
BYBIT_TESTNET=false

# ADDITIONAL SETTINGS
LIVE_TRADING=true
MAX_RISK_PER_TRADE=0.02
MAX_DRAWDOWN=0.30
"""
    
    # Save to secure location
    env_file = Path(".env.secure")
    with open(env_file, "w") as f:
        f.write(env_content)
    
    # Set file permissions (Windows)
    try:
        os.chmod(env_file, 0o600)  # Read/write for owner only
    except:
        pass
    
    print("✅ API credentials saved securely")
    print(f"✅ Saved to: {env_file.absolute()}")
    print()
    print("🔒 SECURITY NOTES:")
    print("- Your API credentials are stored locally only")
    print("- Never share your .env.secure file")
    print("- Use IP restrictions on your Bybit API key")
    print("- Monitor your account regularly")
    print()
    print("🚀 RESTART THE TRADING BOT TO ACTIVATE LIVE TRADING")
    
    return True

if __name__ == "__main__":
    success = setup_api_credentials()
    exit(0 if success else 1)
