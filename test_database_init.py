#!/usr/bin/env python3
"""
Quick database test
"""
try:
    from bybit_bot.database.connection import DatabaseManager
    from bybit_bot.core.config import get_config
    
    print("Loading config...")
    config = get_config()
    
    print("Initializing database...")
    db = DatabaseManager(config)
    
    print("✅ Database initialization successful!")
    
except Exception as e:
    print(f"❌ Database initialization failed: {e}")
    import traceback
    traceback.print_exc()
