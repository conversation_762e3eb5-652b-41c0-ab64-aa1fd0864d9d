@echo off
REM ============================================================================
REM COMPLETE E-DRIVE CONDA ACTIVATION SCRIPT
REM ============================================================================
REM This script activates conda and the bybit-trader environment from E drive
REM ============================================================================

echo [INFO] Activating E-Drive Conda Environment...

REM Set conda paths
set "CONDA_ROOT=E:\conda\miniconda3"
set "CONDA_ENV_PATH=E:\conda\envs\bybit-trader"

REM Initialize conda for this session
call "%CONDA_ROOT%\condabin\conda.bat" activate "%CONDA_ENV_PATH%"

REM Set additional environment variables
set "PYTHONPATH=E:\The_real_deal_copy\Bybit_Bot\BOT"
set "BYBIT_BOT_HOME=E:\The_real_deal_copy\Bybit_Bot\BOT"

REM Verify activation
echo [SUCCESS] E-Drive Conda Environment Activated
echo Python Path: %CONDA_PREFIX%\python.exe
echo Project Path: %BYBIT_BOT_HOME%

REM Test Python
python --version
