import {
    Account<PERSON><PERSON>ce,
    Clear,
    PlayArrow,
    Refresh,
    Settings,
    Speed,
    Stop,
    TrendingUp,
    Visibility,
    VisibilityOff,
    Warning
} from '@mui/icons-material';
import {
    Alert,
    AppBar,
    Box,
    Button,
    Card,
    CardContent,
    Chip,
    Container,
    Divider,
    FormControlLabel,
    Grid,
    IconButton,
    List,
    ListItem,
    ListItemIcon,
    ListItemText,
    Paper,
    Switch,
    Toolbar,
    Tooltip,
    Typography
} from '@mui/material';
import TradingDashboard from './components/TradingDashboard';
import { ElectronProvider, useElectron } from './contexts/ElectronContext';

const Dashboard = () => {
  const {
    isElectron,
    tradingStatus,
    logs,
    isOnline,
    startTrading,
    stopTrading,
    emergencyStop,
    clearLogs
  } = useElectron();

  const [showSystemTray, setShowSystemTray] = useState(true);
  const [showLogs, setShowLogs] = useState(false);

  const handleTrayToggle = (event) => {
    setShowSystemTray(event.target.checked);
    // In a real implementation, this would communicate with the main process
  };

  const getStatusColor = () => {
    if (!isOnline) return 'error';
    if (tradingStatus.isTrading) return 'success';
    return 'warning';
  };

  const getStatusText = () => {
    if (!isOnline) return 'OFFLINE';
    if (tradingStatus.isTrading) return 'TRADING ACTIVE';
    return 'STOPPED';
  };

  return (
    <Container maxWidth="xl" sx={{ mt: 2, mb: 2 }}>
      <Grid container spacing={3}>
        {/* Header Status Card */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Box display="flex" justifyContent="space-between" alignItems="center">
                <Box>
                  <Typography variant="h4" component="h1" gutterBottom>
                    HERMUS Trading Bot
                  </Typography>
                  <Box display="flex" alignItems="center" gap={2}>
                    <Chip
                      label={getStatusText()}
                      color={getStatusColor()}
                      variant="filled"
                      size="large"
                    />
                    {isElectron && (
                      <Chip
                        label="DESKTOP MODE"
                        color="primary"
                        variant="outlined"
                      />
                    )}
                    {!isOnline && (
                      <Alert severity="warning" sx={{ ml: 2 }}>
                        Backend connection unavailable
                      </Alert>
                    )}
                  </Box>
                </Box>
                <Box display="flex" alignItems="center" gap={2}>
                  {isElectron && (
                    <FormControlLabel
                      control={
                        <Switch
                          checked={showSystemTray}
                          onChange={handleTrayToggle}
                          color="primary"
                        />
                      }
                      label="System Tray"
                    />
                  )}
                  <Tooltip title="Refresh Status">
                    <IconButton color="primary">
                      <Refresh />
                    </IconButton>
                  </Tooltip>
                  <Tooltip title="Settings">
                    <IconButton color="primary">
                      <Settings />
                    </IconButton>
                  </Tooltip>
                </Box>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Trading Controls */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Trading Controls
              </Typography>
              <Box display="flex" flexDirection="column" gap={2}>
                <Box display="flex" gap={2}>
                  <Button
                    variant="contained"
                    color="success"
                    startIcon={<PlayArrow />}
                    onClick={startTrading}
                    disabled={tradingStatus.isTrading || !isOnline}
                    fullWidth
                  >
                    Start Trading
                  </Button>
                  <Button
                    variant="contained"
                    color="warning"
                    startIcon={<Stop />}
                    onClick={stopTrading}
                    disabled={!tradingStatus.isTrading}
                    fullWidth
                  >
                    Stop Trading
                  </Button>
                </Box>
                <Button
                  variant="contained"
                  color="error"
                  startIcon={<Warning />}
                  onClick={emergencyStop}
                  disabled={!tradingStatus.isTrading}
                  fullWidth
                >
                  EMERGENCY STOP
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* System Status */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                System Status
              </Typography>
              <List dense>
                <ListItem>
                  <ListItemIcon>
                    <TrendingUp color={tradingStatus.isTrading ? 'success' : 'disabled'} />
                  </ListItemIcon>
                  <ListItemText
                    primary="Trading Engine"
                    secondary={tradingStatus.isTrading ? 'Active' : 'Stopped'}
                  />
                </ListItem>
                <ListItem>
                  <ListItemIcon>
                    <AccountBalance color={isOnline ? 'success' : 'error'} />
                  </ListItemIcon>
                  <ListItemText
                    primary="Backend Connection"
                    secondary={isOnline ? 'Connected' : 'Disconnected'}
                  />
                </ListItem>
                <ListItem>
                  <ListItemIcon>
                    <Speed color="primary" />
                  </ListItemIcon>
                  <ListItemText
                    primary="Process ID"
                    secondary={tradingStatus.processId || 'N/A'}
                  />
                </ListItem>
              </List>
            </CardContent>
          </Card>
        </Grid>

        {/* Logs Section */}
        <Grid item xs={12}>
          <Card>
            <CardContent>
              <Box display="flex" justifyContent="space-between" alignItems="center" mb={2}>
                <Typography variant="h6">
                  System Logs
                </Typography>
                <Box>
                  <IconButton
                    onClick={() => setShowLogs(!showLogs)}
                    color="primary"
                  >
                    {showLogs ? <VisibilityOff /> : <Visibility />}
                  </IconButton>
                  <IconButton
                    onClick={clearLogs}
                    color="secondary"
                  >
                    <Clear />
                  </IconButton>
                </Box>
              </Box>

              {showLogs && (
                <Paper
                  sx={{
                    maxHeight: 300,
                    overflow: 'auto',
                    p: 2,
                    backgroundColor: '#1e1e1e',
                    fontFamily: 'monospace'
                  }}
                >
                  {logs.length === 0 ? (
                    <Typography color="text.secondary">
                      No logs available
                    </Typography>
                  ) : (
                    logs.map((log, index) => (
                      <Box key={index} mb={1}>
                        <Typography
                          variant="body2"
                          color={log.type === 'error' ? 'error' : 'text.primary'}
                          component="pre"
                          sx={{ whiteSpace: 'pre-wrap', wordBreak: 'break-word' }}
                        >
                          [{new Date(log.timestamp).toLocaleTimeString()}] {log.message}
                        </Typography>
                        {index < logs.length - 1 && <Divider sx={{ my: 0.5 }} />}
                      </Box>
                    ))
                  )}
                </Paper>
              )}
            </CardContent>
          </Card>
        </Grid>

        {/* Trading Dashboard */}
        <Grid item xs={12}>
          <TradingDashboard />
        </Grid>
      </Grid>
    </Container>
  );
};

function App() {
  return (
    <ElectronProvider>
      <Box sx={{ flexGrow: 1, minHeight: '100vh' }}>
        <AppBar position="static">
          <Toolbar>
            <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
              HERMUS Trading Bot - Desktop Application
            </Typography>
          </Toolbar>
        </AppBar>
        <Dashboard />
      </Box>
    </ElectronProvider>
  );
}

export default App;
