import React, { useEffect, useRef, useState } from 'react';
import {
  <PERSON>,
  CardContent,
  CardHeader,
  Typography,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Box,
  Chip,
  CircularProgress
} from '@mui/material';
import { TrendingUp } from '@mui/icons-material';

export default function TradingChart({ symbol = 'BTCUSDT', timeframe = '1m' }) {
  const canvasRef = useRef(null);
  const [chartData, setChartData] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedTimeframe, setSelectedTimeframe] = useState(timeframe);

  useEffect(() => {
    // Simulate chart data generation
    const generateChartData = () => {
      const data = [];
      let price = 45000 + Math.random() * 1000;
      
      for (let i = 0; i < 100; i++) {
        price += (Math.random() - 0.5) * 200;
        data.push({
          time: Date.now() - (100 - i) * 60000,
          price: Math.max(40000, Math.min(50000, price)),
          volume: Math.random() * 1000
        });
      }
      
      setChartData(data);
      setIsLoading(false);
    };

    generateChartData();
    const interval = setInterval(generateChartData, 30000); // Update every 30s

    return () => clearInterval(interval);
  }, [symbol, selectedTimeframe]);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas || chartData.length === 0) return;

    const ctx = canvas.getContext('2d');
    const { width, height } = canvas;

    // Clear canvas
    ctx.clearRect(0, 0, width, height);

    // Chart styling
    const padding = 40;
    const chartWidth = width - padding * 2;
    const chartHeight = height - padding * 2;

    // Calculate price range
    const prices = chartData.map(d => d.price);
    const minPrice = Math.min(...prices);
    const maxPrice = Math.max(...prices);
    const priceRange = maxPrice - minPrice;

    // Draw grid
    ctx.strokeStyle = '#2a2d3a';
    ctx.lineWidth = 1;
    
    // Horizontal grid lines
    for (let i = 0; i <= 5; i++) {
      const y = padding + (chartHeight / 5) * i;
      ctx.beginPath();
      ctx.moveTo(padding, y);
      ctx.lineTo(width - padding, y);
      ctx.stroke();
    }

    // Vertical grid lines
    for (let i = 0; i <= 10; i++) {
      const x = padding + (chartWidth / 10) * i;
      ctx.beginPath();
      ctx.moveTo(x, padding);
      ctx.lineTo(x, height - padding);
      ctx.stroke();
    }

    // Draw price line
    ctx.strokeStyle = '#00ff88';
    ctx.lineWidth = 2;
    ctx.beginPath();

    chartData.forEach((point, index) => {
      const x = padding + (index / (chartData.length - 1)) * chartWidth;
      const y = padding + (1 - (point.price - minPrice) / priceRange) * chartHeight;

      if (index === 0) {
        ctx.moveTo(x, y);
      } else {
        ctx.lineTo(x, y);
      }
    });

    ctx.stroke();

    // Draw volume bars at bottom
    const volumeHeight = 50;
    const maxVolume = Math.max(...chartData.map(d => d.volume));
    
    ctx.fillStyle = 'rgba(0, 255, 136, 0.3)';
    chartData.forEach((point, index) => {
      const x = padding + (index / (chartData.length - 1)) * chartWidth;
      const barHeight = (point.volume / maxVolume) * volumeHeight;
      const y = height - padding - barHeight;
      
      ctx.fillRect(x - 1, y, 2, barHeight);
    });

    // Draw labels
    ctx.fillStyle = '#b5b5b5';
    ctx.font = '12px Roboto';
    ctx.textAlign = 'center';

    // Price labels
    for (let i = 0; i <= 5; i++) {
      const price = minPrice + (priceRange / 5) * (5 - i);
      const y = padding + (chartHeight / 5) * i;
      ctx.fillText(`$${price.toFixed(0)}`, 20, y + 4);
    }

    // Symbol and info
    ctx.fillStyle = '#ffffff';
    ctx.font = 'bold 16px Roboto';
    ctx.textAlign = 'left';
    ctx.fillText(symbol, padding, 25);
    
    const currentPrice = chartData[chartData.length - 1]?.price || 0;
    const priceChange = chartData.length > 1 ? 
      ((currentPrice - chartData[0].price) / chartData[0].price * 100) : 0;
    
    ctx.fillStyle = priceChange >= 0 ? '#00ff88' : '#ff4747';
    ctx.font = '14px Roboto';
    ctx.fillText(`$${currentPrice.toFixed(2)} (${priceChange.toFixed(2)}%)`, padding + 100, 25);

  }, [chartData, symbol]);

  if (isLoading) {
    return (
      <Card sx={{ height: 400 }}>
        <CardContent sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', height: '100%' }}>
          <CircularProgress />
        </CardContent>
      </Card>
    );
  }

  const currentPrice = chartData[chartData.length - 1]?.price || 0;
  const priceChange = chartData.length > 1 ? 
    ((currentPrice - chartData[0].price) / chartData[0].price * 100) : 0;

  return (
    <Card>
      <CardHeader
        title={
          <Box display="flex" alignItems="center" gap={1}>
            <TrendingUp />
            <Typography variant="h6">{symbol} Chart</Typography>
          </Box>
        }
        action={
          <Box display="flex" alignItems="center" gap={2}>
            <FormControl size="small" sx={{ minWidth: 80 }}>
              <InputLabel>Timeframe</InputLabel>
              <Select
                value={selectedTimeframe}
                label="Timeframe"
                onChange={(e) => setSelectedTimeframe(e.target.value)}
              >
                <MenuItem value="1m">1m</MenuItem>
                <MenuItem value="5m">5m</MenuItem>
                <MenuItem value="15m">15m</MenuItem>
                <MenuItem value="1h">1h</MenuItem>
                <MenuItem value="4h">4h</MenuItem>
                <MenuItem value="1d">1d</MenuItem>
              </Select>
            </FormControl>
            <Chip
              label="LIVE"
              color="success"
              variant="filled"
              size="small"
            />
          </Box>
        }
        subheader={
          <Typography
            variant="subtitle1"
            color={priceChange >= 0 ? 'success.main' : 'error.main'}
          >
            ${currentPrice.toFixed(2)} ({priceChange >= 0 ? '+' : ''}{priceChange.toFixed(2)}%)
          </Typography>
        }
      />
      <CardContent>
        <canvas
          ref={canvasRef}
          width={800}
          height={320}
          style={{ 
            width: '100%', 
            height: '320px',
            backgroundColor: '#1a1a1a',
            borderRadius: '4px'
          }}
        />
      </CardContent>
    </Card>
  );
}
