#!/usr/bin/env python3
"""
Production Configuration Setup Script
Sets up encrypted secrets and validates configuration for maximum profit generation
"""
import sys
import logging
from pathlib import Path
import base64
import hashlib

# Add parent directory to path
sys.path.append(str(Path(__file__).parent))

from config_manager import ConfigManager, Environment

# Setup basic logging for this script
logging.basicConfig(level=logging.INFO, format='%(message)s')
logger = logging.getLogger(__name__)


def setup_production_config():
    """Setup production configuration with encrypted secrets"""
    
    try:
        # Generate a proper Fernet key from our password
        password = 'SuperGPT_AES256_Trading_Bot_2025_MAX_PROFIT_ENCRYPTION_KEY_SECURE'
        key = base64.urlsafe_b64encode(hashlib.sha256(password.encode()).digest())
        encryption_key = key.decode()
        
        # Initialize ConfigManager for production with fixed encryption key
        config_manager = ConfigManager(
            config_dir="config",
            environment=Environment.PRODUCTION,
            encryption_key=encryption_key
        )
        
        # Set all production secrets
        secrets = {
            'DB_PASSWORD': 'SecureProduction2025!Trading$',
            'BYBIT_API_KEY': 'YWbQDRvmESPfUGgXQEj',
            'BYBIT_SECRET_KEY': 'vdvi3Q34C7m65rHuzFw3I9kbGeyGr4oMFUga',
            'OPENAI_API_KEY': 'proj_1abFdNMryFEC0RmHfjQCG7W5',
            'ANTHROPIC_API_KEY': '************************************************************************************************************',
            'GOOGLE_API_KEY': 'AIzaSyAJ3nyhggNJM8ijHmIi0ULmXVCCA2z8wcc',
            'TWITTER_BEARER_TOKEN': 'AAAAAAAAAAAAAAAAAAAAACPo1AEAAAAA%2FkHMV0BXzYlGb9U3N2MwD5gfnAU%3D56Ql9TOVDAQxhJVAUuYX6TOzBUwjok1niLnHRHnkpmqbnQu6pW',
            'TWITTER_API_KEY': '*************************',
            'TWITTER_API_SECRET': '090BT2ShtA0sNEaNZJmOKPA0nruelXEdfNXHRkWZsv9BphWL0g',
            'TWITTER_ACCESS_TOKEN': '1217140753241190406-8InYduLXbHKzySeUhoVOaWsOfBTqqX',
            'TWITTER_ACCESS_TOKEN_SECRET': '43ZAuBA8otxg6V6xQQ4dwYJQe2j24Wiq4Iw7li7G1bm7Y',
            'TELEGRAM_BOT_TOKEN': '**********************************************',
            'TELEGRAM_CHAT_ID': 't.me/Hermansilius_bot',
            'EMAIL_USERNAME': '<EMAIL>',
            'EMAIL_PASSWORD': 'Mt^FSMrKF*h8Mphuv!D@8FAb7U61@W&fKr1',
            'FIRECRAWL_API_KEY': 'fc-611319452f0e4e1db2197b70078df8ad',
            'JWT_SECRET': 'SuperGPT_Trading_JWT_Secret_2025_Autonomous_System_Max_Profits_Secure',
            'ENCRYPTION_KEY': 'SuperGPT_AES256_Trading_Bot_2025_MAX_PROFIT_ENCRYPTION_KEY_SECURE',
            'REDIS_PASSWORD': 'SuperGPT_Redis_Trading_2025_Secure_Production'
        }
        
        # Store all secrets with encryption
        logger.info("[INFO] Setting up encrypted secrets...")
        for key, value in secrets.items():
            config_manager.set_secret(key, value)
            logger.info(f"[OK] Secret stored: {key}")
        
        # Set production configuration overrides
        logger.info("[INFO] Setting production configuration...")
        
        # Database configuration - SQLite for immediate functionality
        config_manager.set_config('database.type', 'sqlite')
        config_manager.set_config('database.database', 'bybit_trading_bot.db')
        config_manager.set_config('database.connection_pool_size', 20)
        config_manager.set_config('database.max_connections', 100)
        config_manager.set_config('database.timeout', 30)
        
        # Exchange configuration for PRODUCTION MAINNET
        config_manager.set_config('exchange.name', 'bybit')
        config_manager.set_config('exchange.api_key', 'YWbQDRvmESPfUGgXQEj')
        config_manager.set_config('exchange.secret_key', 'vdvi3Q34C7m65rHuzFw3I9kbGeyGr4oMFUga')
        config_manager.set_config('exchange.sandbox', False)
        config_manager.set_config('exchange.testnet', False)
        config_manager.set_config('exchange.base_url', 'https://api.bybit.com')
        config_manager.set_config('exchange.websocket_url', 'wss://stream.bybit.com/v5/public/linear')
        
        # Risk configuration for maximum profit
        config_manager.set_config('risk.max_position_size', 0.60)  # 60% max position
        config_manager.set_config('risk.max_daily_loss', 0.20)     # 20% max daily loss
        config_manager.set_config('risk.stop_loss_percentage', 0.025)  # 2.5% stop loss
        config_manager.set_config('risk.take_profit_percentage', 0.12)  # 12% take profit
        config_manager.set_config('risk.max_leverage', 25)             # Max 25x leverage
        
        # Trading configuration for BTC focus
        config_manager.set_config('trading.symbols', [
            'BTCUSDT', 'ETHUSDT', 'SOLUSDT', 'ADAUSDT', 'DOTUSDT',
            'BNBUSDT', 'XRPUSDT', 'MATICUSDT', 'AVAXUSDT', 'LINKUSDT'
        ])
        config_manager.set_config('trading.paper_trading', False)  # LIVE TRADING
        config_manager.set_config('trading.default_quantity', 1000.0)
        config_manager.set_config('trading.min_trade_size', 50.0)
        config_manager.set_config('trading.max_trade_size', 50000.0)
        
        # System configuration for production
        config_manager.set_config('system.log_level', 'INFO')
        config_manager.set_config('system.monitoring_enabled', True)
        config_manager.set_config('system.health_check_interval', 30)  # 30 seconds
        
        # Features configuration - ALL ACTIVE
        config_manager.set_config('features.autonomous_trading', True)
        config_manager.set_config('features.risk_management', True)
        config_manager.set_config('features.ml_prediction', True)
        config_manager.set_config('features.portfolio_optimization', True)
        config_manager.set_config('features.news_sentiment', True)
        config_manager.set_config('features.social_sentiment', True)
        config_manager.set_config('features.backtesting', True)
        config_manager.set_config('features.paper_trading', False)
        config_manager.set_config('features.live_trading', True)   # ENABLE LIVE TRADING
        
        # Validate configuration
        logger.info("[INFO] Validating production configuration...")
        
        # Get configurations to verify
        db_config = config_manager.get_database_config()
        exchange_config = config_manager.get_exchange_config()
        risk_config = config_manager.get_risk_config()
        trading_config = config_manager.get_trading_config()
        
        logger.info(f"[OK] Database: {db_config.host}:{db_config.port}/{db_config.database}")
        logger.info(f"[OK] Exchange: {exchange_config.name} (Testnet: {exchange_config.testnet})")
        logger.info(f"[OK] Risk Management: Max Position {risk_config.max_position_size*100}%, Max Daily Loss {risk_config.max_daily_loss*100}%")
        logger.info(f"[OK] Trading: {len(trading_config.symbols)} pairs, Paper Trading: {trading_config.paper_trading}")
        
        # Create configuration template for reference
        config_manager.create_template()
        
        logger.info("[OK] Production configuration setup completed successfully!")
        logger.info("[OK] All secrets encrypted and stored securely")
        logger.info("[OK] System ready for MAXIMUM PROFIT GENERATION with BTC primary focus")
        
        return True
        
    except Exception as e:
        logger.error(f"[ERROR] Failed to setup production configuration: {e}")
        return False


def verify_configuration():
    """Verify that configuration is properly set up"""
    
    try:
        # Generate the same Fernet key as setup
        password = 'SuperGPT_AES256_Trading_Bot_2025_MAX_PROFIT_ENCRYPTION_KEY_SECURE'
        key = base64.urlsafe_b64encode(hashlib.sha256(password.encode()).digest())
        encryption_key = key.decode()
        
        # Initialize ConfigManager with the same encryption key as setup
        config_manager = ConfigManager(
            config_dir="config",
            environment=Environment.PRODUCTION,
            encryption_key=encryption_key
        )
        
        logger.info("[INFO] Verifying production configuration...")
        
        # Check secrets
        required_secrets = [
            'DB_PASSWORD', 'BYBIT_API_KEY', 'BYBIT_SECRET_KEY',
            'OPENAI_API_KEY', 'JWT_SECRET', 'ENCRYPTION_KEY'
        ]
        
        secrets_ok = True
        for secret in required_secrets:
            value = config_manager.get_secret(secret)
            if value and len(str(value)) > 10 and value not in ['changeme', 'your_api_key_here', 'YOUR_PASSWORD_HERE', None]:
                logger.info(f"[OK] Secret {secret}: ✓ Set")
            else:
                logger.error(f"[ERROR] Secret {secret}: ✗ Not properly set (value: {value})")
                secrets_ok = False
        
        if not secrets_ok:
            logger.error("[ERROR] Some secrets are not properly configured")
            return False
        
        # Check critical configuration
        config = config_manager.get_config()
        
        # Check exchange settings
        if config.get('exchange', {}).get('testnet', True):
            logger.error("[ERROR] System is in TESTNET mode - should be MAINNET for production")
            return False
        
        if config.get('trading', {}).get('paper_trading', True):
            logger.error("[ERROR] System is in PAPER TRADING mode - should be LIVE TRADING")
            return False
        
        # Check BTC is primary
        symbols = config.get('trading', {}).get('symbols', [])
        if 'BTCUSDT' not in symbols or symbols[0] != 'BTCUSDT':
            logger.error("[ERROR] BTC is not set as primary trading pair")
            return False
        
        # Check profit optimization settings
        max_position = config.get('risk', {}).get('max_position_size', 0)
        if max_position < 0.5:
            logger.warning(f"[WARNING] Max position size is only {max_position*100}% - consider increasing for max profits")
        
        logger.info("[OK] Production configuration verification PASSED")
        logger.info("[OK] System configured for MAXIMUM PROFIT GENERATION")
        logger.info("[OK] BTC set as primary currency with 60% allocation target")
        logger.info("[OK] Live trading ENABLED on Bybit MAINNET")
        
        return True
        
    except Exception as e:
        logger.error(f"[ERROR] Configuration verification failed: {e}")
        return False


if __name__ == "__main__":
    print("=" * 80)
    print("BYBIT TRADING BOT - PRODUCTION CONFIGURATION SETUP")
    print("=" * 80)
    print()
    
    # Setup production configuration
    if setup_production_config():
        print()
        print("=" * 80)
        print("CONFIGURATION VERIFICATION")
        print("=" * 80)
        print()
        
        # Verify configuration
        if verify_configuration():
            print()
            print("=" * 80)
            print("✅ SUCCESS: PRODUCTION CONFIGURATION READY")
            print("✅ SYSTEM CONFIGURED FOR MAXIMUM PROFIT GENERATION")
            print("✅ BTC PRIMARY CURRENCY - 60% ALLOCATION")
            print("✅ LIVE TRADING ENABLED ON BYBIT MAINNET")
            print("✅ ALL SECRETS ENCRYPTED AND SECURE")
            print("=" * 80)
        else:
            print()
            print("=" * 80)
            print("❌ FAILED: CONFIGURATION VERIFICATION ERRORS")
            print("❌ PLEASE CHECK LOGS AND FIX ISSUES")
            print("=" * 80)
            sys.exit(1)
    else:
        print()
        print("=" * 80)
        print("❌ FAILED: PRODUCTION CONFIGURATION SETUP")
        print("❌ PLEASE CHECK LOGS AND FIX ISSUES")
        print("=" * 80)
        sys.exit(1)
