
#!/usr/bin/env python3
import asyncio
import psutil
import sqlite3
from datetime import datetime

async def check_system_health():
    '''Check system health and report status'''
    print("[INFO] Running system health check...")
    
    # Check memory usage
    memory = psutil.virtual_memory()
    print(f"[INFO] Memory usage: {memory.percent:.1f}%")
    
    # Check CPU usage
    cpu_percent = psutil.cpu_percent(interval=1)
    print(f"[INFO] CPU usage: {cpu_percent:.1f}%")
    
    # Check database connectivity
    try:
        conn = sqlite3.connect("bybit_trading_bot.db")
        cursor = conn.cursor()
        cursor.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='table'")
        table_count = cursor.fetchone()[0]
        conn.close()
        print(f"[OK] Database accessible with {table_count} tables")
    except Exception as e:
        print(f"[ERROR] Database check failed: {e}")
    
    # Check disk space
    disk = psutil.disk_usage('.')
    disk_percent = (disk.used / disk.total) * 100
    print(f"[INFO] Disk usage: {disk_percent:.1f}%")
    
    print("[OK] System health check completed")

if __name__ == "__main__":
    asyncio.run(check_system_health())
