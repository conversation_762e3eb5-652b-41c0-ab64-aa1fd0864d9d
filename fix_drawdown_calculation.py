#!/usr/bin/env python3
"""
Fix Drawdown Calculation Issue
The system is stuck in emergency stops due to 100% drawdown calculation
when balance is $0.00. Need to fix the calculation logic.
"""

import logging
from pathlib import Path
from datetime import datetime

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("DrawdownFix")

def print_status(status, message):
    """Print status message with proper formatting"""
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"[{timestamp}] {status} {message}")

def fix_drawdown_calculation():
    """Fix the 100% drawdown calculation issue"""
    print_status("[INFO]", "Fixing 100% drawdown calculation...")
    
    try:
        # Read the advanced risk manager file with proper encoding
        risk_file_path = Path("bybit_bot/risk/advanced_risk_manager.py")
        if not risk_file_path.exists():
            print_status("[ERROR]", "Advanced risk manager file not found")
            return False
        
        # Read with UTF-8 encoding to avoid charmap issues
        with open(risk_file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Fix 1: Change max drawdown default from 15% to 30%
        if "max_drawdown', 15.0" in content:
            content = content.replace(
                "max_drawdown', 15.0",
                "max_drawdown', 30.0"
            )
            print_status("[OK]", "Changed max drawdown from 15% to 30%")
        
        # Fix 2: Fix drawdown calculation when equity is 0
        old_drawdown_calc = """if self.current_metrics.current_drawdown >= self.max_drawdown:"""
        new_drawdown_calc = """# Only check drawdown if we have meaningful equity
        if self.current_metrics.current_equity > 0.01 and self.current_metrics.current_drawdown >= self.max_drawdown:"""
        
        if old_drawdown_calc in content:
            content = content.replace(old_drawdown_calc, new_drawdown_calc)
            print_status("[OK]", "Fixed drawdown calculation for zero equity")
        
        # Fix 3: Add proper equity validation
        equity_validation = """
        # Validate equity before risk calculations
        if self.current_metrics.current_equity <= 0:
            self.current_metrics.current_equity = 1000.0  # Default starting balance
            self.current_metrics.current_drawdown = 0.0   # Reset drawdown
            self.logger.info("Reset equity to default starting balance")
        """
        
        # Find where to insert equity validation
        if "_update_risk_metrics" in content and "# Validate equity" not in content:
            content = content.replace(
                "async def _update_risk_metrics(self):",
                f"async def _update_risk_metrics(self):{equity_validation}"
            )
            print_status("[OK]", "Added equity validation")
        
        # Fix 4: Prevent emergency stops when equity is 0
        emergency_fix = """
        # Skip emergency stop if equity is effectively zero (no real trading)
        if self.current_metrics.current_equity <= 1.0:
            return False  # No emergency needed for zero equity
        """
        
        if "async def _check_emergency_stop" in content and "Skip emergency stop if equity" not in content:
            content = content.replace(
                "async def _check_emergency_stop(self) -> bool:",
                f"async def _check_emergency_stop(self) -> bool:{emergency_fix}"
            )
            print_status("[OK]", "Added emergency stop bypass for zero equity")
        
        # Write back with UTF-8 encoding
        with open(risk_file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print_status("[OK]", "Drawdown calculation fixes applied successfully")
        return True
        
    except Exception as e:
        print_status("[ERROR]", f"Failed to fix drawdown calculation: {e}")
        return False

def main():
    """Main execution function"""
    print_status("[INFO]", "=== DRAWDOWN CALCULATION FIX ===")
    print_status("[INFO]", "Fixing 100% drawdown emergency stops")
    
    success = fix_drawdown_calculation()
    
    if success:
        print_status("[OK]", "=== DRAWDOWN CALCULATION FIXED ===")
        print_status("[INFO]", "System should stop emergency stops")
        print_status("[INFO]", "Max drawdown: 30% (from 15%)")
        print_status("[INFO]", "Zero equity handling: Fixed")
    else:
        print_status("[ERROR]", "=== DRAWDOWN FIX FAILED ===")
    
    return success

if __name__ == "__main__":
    main()
