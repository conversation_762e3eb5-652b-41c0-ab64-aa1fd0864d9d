@echo off
echo [INFO] Starting Mobile App Server with Logo Integration...
echo [INFO] Activating environment...
call E:\conda\scripts\activate_bybit_trader.bat

echo [INFO] Checking if logo files exist...
if exist "static\logo.svg" (
    echo [OK] Logo SVG found
) else (
    echo [WARNING] Logo SVG not found
)

if exist "static\icon-192x192.png" (
    echo [OK] App icon found
) else (
    echo [WARNING] App icon not found - creating fallback
    python create_icon.py
)

echo [INFO] Starting mobile app server on http://localhost:8000...
echo [INFO] Mobile app will be available at:
echo       - Local: http://localhost:8000/mobile
echo       - Network: http://**************:8000/mobile (if accessible)
echo [INFO] Logo integration active - check header for stylized portrait
echo.
python mobile_app_server.py
