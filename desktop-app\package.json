{"name": "hermus-desktop", "version": "1.0.0", "description": "HERMUS Bybit Trading Bot - Professional Desktop Application", "main": "main.js", "homepage": "./", "scripts": {"dev": "concurrently \"vite\" \"electron .\"", "build": "vite build", "preview": "vite preview", "electron": "electron .", "electron-dev": "ELECTRON_IS_DEV=true electron .", "pack": "electron-builder --dir", "dist": "electron-builder", "dist-win": "electron-builder --win", "postinstall": "electron-builder install-app-deps"}, "build": {"appId": "com.hermus.trading-bot", "productName": "HERMUS Trading Bot", "directories": {"output": "dist"}, "files": ["dist/**/*", "main.js", "preload.js", "package.json"], "win": {"target": "nsis", "icon": "../static/icon-192x192.png"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true}}, "dependencies": {"electron-updater": "^6.1.7", "axios": "^1.4.0", "react": "^18.2.0", "react-dom": "^18.2.0", "@mui/material": "^5.14.20", "@mui/icons-material": "^5.14.19", "@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "react-toastify": "^9.1.3", "recharts": "^2.8.0", "date-fns": "^2.30.0"}, "devDependencies": {"electron": "^28.0.0", "electron-builder": "^24.9.1", "vite": "^5.0.0", "@vitejs/plugin-react": "^4.2.0", "concurrently": "^8.2.2"}}