@echo off
echo ========================================
echo HERMUS Desktop Application Setup
echo ========================================
echo.

REM Change to E: drive
E:
cd /d "E:\The_real_deal_copy\Bybit_Bot\BOT"

echo [1/6] Activating bybit-trader conda environment...
call conda activate bybit-trader
if errorlevel 1 (
    echo ERROR: Failed to activate bybit-trader environment
    pause
    exit /b 1
)

echo [2/6] Installing Node.js dependencies in desktop-app...
cd desktop-app
if not exist "package.json" (
    echo ERROR: package.json not found in desktop-app directory
    pause
    exit /b 1
)

echo Installing npm dependencies...
call npm install
if errorlevel 1 (
    echo ERROR: Failed to install npm dependencies
    pause
    exit /b 1
)

echo [3/6] Building React application...
call npm run build
if errorlevel 1 (
    echo ERROR: Failed to build React application
    pause
    exit /b 1
)

echo [4/6] Installing Electron dependencies...
call npm install electron electron-builder --save-dev
if errorlevel 1 (
    echo ERROR: Failed to install Electron dependencies
    pause
    exit /b 1
)

echo [5/6] Copying logo files...
cd ..
if exist "static\icon-192x192.png" (
    copy "static\icon-192x192.png" "desktop-app\"
    echo Logo copied successfully
) else (
    echo WARNING: Logo file not found, using default
)

echo [6/6] Creating desktop shortcuts...
cd desktop-app

echo [SETUP COMPLETE]
echo.
echo Desktop application is ready!
echo.
echo Available commands:
echo - launch_hermus_desktop.bat    : Start production desktop app
echo - dev_hermus_desktop.bat       : Start development mode
echo - copy_frontend_components.bat : Sync components from gui/
echo.
echo Press any key to launch the desktop application...
pause > nul

REM Launch the desktop application
cd ..
call launch_hermus_desktop.bat
