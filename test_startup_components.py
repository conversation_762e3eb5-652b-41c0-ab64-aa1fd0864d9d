#!/usr/bin/env python3
"""
Quick startup test to identify remaining issues
"""
import sys
import traceback

def test_system_startup():
    print("🔥 TESTING UNIFIED SYSTEM STARTUP...")
    print("=" * 50)
    
    try:
        # Test 1: Configuration
        print("Testing Configuration...")
        from bybit_bot.core.config import BotConfig, EnhancedBotConfig
        config = BotConfig()
        print(f"✅ Configuration: {type(config)}")
        
        # Test 2: Database Manager
        print("Testing Database Manager...")
        from bybit_bot.database.connection import DatabaseManager
        db_manager = DatabaseManager(config)
        print(f"✅ Database Manager: {type(db_manager)}")
        
        # Test 3: MetaCognitionEngine with keyword args
        print("Testing MetaCognitionEngine...")
        from bybit_bot.ai.meta_cognition_engine import MetaCognitionEngine
        meta_engine = MetaCognitionEngine(config=config, database_manager=db_manager)
        print(f"✅ MetaCognitionEngine: {type(meta_engine)}")
        
        # Test 4: SuperGPT Integration
        print("Testing SuperGPT Integration...")
        from bybit_bot.ai.supergpt_integration import SuperGPTIntegration
        supergpt = SuperGPTIntegration(bot_config=config, database_manager=db_manager)
        print(f"✅ SuperGPT Integration: {type(supergpt)}")
        
        # Test 5: Bybit Client
        print("Testing Bybit Client...")
        from bybit_bot.exchange.bybit_client import BybitClient
        client = BybitClient(config)
        print(f"✅ Bybit Client: {type(client)}")
        
        # Test 6: Risk Manager
        print("Testing Risk Manager...")
        from bybit_bot.risk.advanced_risk_manager import AdvancedRiskManager
        risk_manager = AdvancedRiskManager(config, db_manager, client)
        print(f"✅ Risk Manager: {type(risk_manager)}")
        
        print("\n🎉 ALL CRITICAL COMPONENTS INITIALIZED SUCCESSFULLY!")
        return True
        
    except Exception as e:
        print(f"\n❌ ERROR: {str(e)}")
        print("\nFull traceback:")
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_system_startup()
    sys.exit(0 if success else 1)
