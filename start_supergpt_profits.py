#!/usr/bin/env python3
"""
SUPERGPT MAXIMUM PROFIT TRADING SYSTEM
Final startup script with all functions activated
"""

import asyncio
import os
from datetime import datetime

# Set environment variables for maximum profit mode
os.environ["SUPERGPT_ENABLED"] = "true"
os.environ["MAXIMUM_PROFIT_MODE"] = "true"
os.environ["AGGRESSIVE_TRADING"] = "true"
os.environ["ALL_FUNCTIONS_ACTIVE"] = "true"

print("[OK] STARTING SUPERGPT MAXIMUM PROFIT SYSTEM")
print("=" * 60)
print(f"Timestamp: {datetime.now()}")
print("Mode: SUPERGPT + MAXIMUM PROFIT GENERATION")
print("All Functions: ACTIVE")
print("=" * 60)

async def main():
    """Main startup function"""
    try:
        # Import the unified system
        from main_unified_system import UnifiedTradingSystem
        
        print("[STARTING] Initializing SuperGPT Trading System...")
        system = UnifiedTradingSystem()
        
        print("[ACTIVATING] All SuperGPT functions...")
        print("[ACTIVATING] Maximum profit strategies...")
        print("[ACTIVATING] Real-time monitoring...")
        
        print("[STARTING] Profit generation...")
        await system.start()
        
        print("[OK] SUPERGPT SYSTEM OPERATIONAL - GENERATING PROFITS!")
        print("[INFO] All functions active, profit generation commenced")
        
        # Keep system running and report profits
        profit_counter = 0
        while True:
            await asyncio.sleep(60)  # Check every minute
            profit_counter += 1
            
            try:
                if hasattr(system, 'bot_manager') and system.bot_manager:
                    status = await system.bot_manager.get_status()
                    pnl = status.get('trading_metrics', {}).get('total_pnl', 0)
                    positions = status.get('trading_metrics', {}).get('active_positions_count', 0)
                    print(f"[PROFIT] [{profit_counter:03d}] P&L: ${pnl:.2f} | Positions: {positions} | SuperGPT: ACTIVE")
            except Exception as e:
                print(f"[WARNING] Status check error: {e}")
    
    except KeyboardInterrupt:
        print("\n[STOP] Stopping SuperGPT system...")
        if 'system' in locals():
            await system.shutdown()
    except Exception as e:
        print(f"[ERROR] CRITICAL ERROR: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
