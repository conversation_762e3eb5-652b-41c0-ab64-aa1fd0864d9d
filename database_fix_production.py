#!/usr/bin/env python3
"""
Database Schema Fix Script for Production
Fixes critical database schema issues for maximum profit generation
"""

import sys
import logging
import psycopg2
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent))

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(message)s')
logger = logging.getLogger(__name__)

def fix_database_schema():
    """Fix database schema issues for profit generation"""
    
    print("="*80)
    print("BYBIT TRADING BOT - DATABASE SCHEMA FIX")
    print("="*80)
    
    try:
        # Database connection parameters
        db_params = {
            'host': 'localhost',
            'port': 5432,
            'database': 'bybit_trading_bot',
            'user': 'postgres',
            'password': 'SecureProduction2025!Trading$'
        }
        
        logger.info("[INFO] Connecting to PostgreSQL database...")
        conn = psycopg2.connect(**db_params)
        conn.autocommit = True
        cursor = conn.cursor()
        
        logger.info("[OK] Connected to database successfully")
        
        # Check if database exists, create if not
        cursor.execute("SELECT 1 FROM pg_database WHERE datname = 'bybit_trading_bot'")
        if not cursor.fetchone():
            logger.info("[INFO] Creating database bybit_trading_bot...")
            cursor.execute("CREATE DATABASE bybit_trading_bot")
            logger.info("[OK] Database created")
        
        # Reconnect to the specific database
        conn.close()
        conn = psycopg2.connect(**db_params)
        conn.autocommit = True
        cursor = conn.cursor()
        
        # Fix 1: Add missing metadata column to positions table
        logger.info("[INFO] Fixing positions table schema...")
        
        # Check if positions table exists
        cursor.execute("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = 'positions'
            )
        """)
        
        if cursor.fetchone()[0]:
            # Check if metadata column exists
            cursor.execute("""
                SELECT EXISTS (
                    SELECT FROM information_schema.columns 
                    WHERE table_schema = 'public' 
                    AND table_name = 'positions' 
                    AND column_name = 'metadata'
                )
            """)
            
            if not cursor.fetchone()[0]:
                logger.info("[INFO] Adding metadata column to positions table...")
                cursor.execute("ALTER TABLE positions ADD COLUMN metadata JSONB DEFAULT '{}'::jsonb")
                logger.info("[OK] Added metadata column")
            else:
                logger.info("[OK] Metadata column already exists")
        else:
            logger.info("[INFO] Creating positions table...")
            cursor.execute("""
                CREATE TABLE positions (
                    id SERIAL PRIMARY KEY,
                    symbol VARCHAR(20) NOT NULL,
                    side VARCHAR(10) NOT NULL,
                    size DECIMAL(20,8) NOT NULL,
                    entry_price DECIMAL(20,8),
                    current_price DECIMAL(20,8),
                    unrealized_pnl DECIMAL(20,8) DEFAULT 0,
                    realized_pnl DECIMAL(20,8) DEFAULT 0,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    status VARCHAR(20) DEFAULT 'active',
                    metadata JSONB DEFAULT '{}'::jsonb,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            logger.info("[OK] Created positions table")
        
        # Fix 2: Create or update trades table with proper foreign key
        logger.info("[INFO] Fixing trades table schema...")
        
        cursor.execute("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = 'trades'
            )
        """)
        
        if not cursor.fetchone()[0]:
            logger.info("[INFO] Creating trades table...")
            cursor.execute("""
                CREATE TABLE trades (
                    id SERIAL PRIMARY KEY,
                    position_id INTEGER REFERENCES positions(id) ON DELETE CASCADE,
                    symbol VARCHAR(20) NOT NULL,
                    side VARCHAR(10) NOT NULL,
                    quantity DECIMAL(20,8) NOT NULL,
                    price DECIMAL(20,8) NOT NULL,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    commission DECIMAL(20,8) DEFAULT 0,
                    realized_pnl DECIMAL(20,8) DEFAULT 0,
                    trade_type VARCHAR(20) DEFAULT 'market',
                    order_id VARCHAR(100),
                    metadata JSONB DEFAULT '{}'::jsonb,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            logger.info("[OK] Created trades table")
        
        # Fix 3: Create market_data table for real-time data
        logger.info("[INFO] Creating market_data table...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS market_data (
                id SERIAL PRIMARY KEY,
                symbol VARCHAR(20) NOT NULL,
                timestamp TIMESTAMP NOT NULL,
                open_price DECIMAL(20,8),
                high_price DECIMAL(20,8),
                low_price DECIMAL(20,8),
                close_price DECIMAL(20,8),
                volume DECIMAL(20,8),
                turnover DECIMAL(20,8),
                interval_type VARCHAR(10),
                metadata JSONB DEFAULT '{}'::jsonb,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # Create index for fast lookups
        cursor.execute("""
            CREATE INDEX IF NOT EXISTS idx_market_data_symbol_timestamp 
            ON market_data(symbol, timestamp DESC)
        """)
        logger.info("[OK] Market data table ready")
        
        # Fix 4: Create account_balance table
        logger.info("[INFO] Creating account_balance table...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS account_balance (
                id SERIAL PRIMARY KEY,
                currency VARCHAR(20) NOT NULL,
                available_balance DECIMAL(20,8) DEFAULT 0,
                used_balance DECIMAL(20,8) DEFAULT 0,
                total_balance DECIMAL(20,8) DEFAULT 0,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                metadata JSONB DEFAULT '{}'::jsonb
            )
        """)
        logger.info("[OK] Account balance table ready")
        
        # Fix 5: Create performance_metrics table
        logger.info("[INFO] Creating performance_metrics table...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS performance_metrics (
                id SERIAL PRIMARY KEY,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                total_pnl DECIMAL(20,8) DEFAULT 0,
                daily_pnl DECIMAL(20,8) DEFAULT 0,
                win_rate DECIMAL(5,2) DEFAULT 0,
                total_trades INTEGER DEFAULT 0,
                profitable_trades INTEGER DEFAULT 0,
                max_drawdown DECIMAL(5,2) DEFAULT 0,
                sharpe_ratio DECIMAL(10,4) DEFAULT 0,
                roi DECIMAL(5,2) DEFAULT 0,
                metadata JSONB DEFAULT '{}'::jsonb
            )
        """)
        logger.info("[OK] Performance metrics table ready")
        
        # Fix 6: Create system_health table
        logger.info("[INFO] Creating system_health table...")
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS system_health (
                id SERIAL PRIMARY KEY,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                api_status VARCHAR(20) DEFAULT 'unknown',
                database_status VARCHAR(20) DEFAULT 'unknown',
                memory_usage DECIMAL(5,2) DEFAULT 0,
                cpu_usage DECIMAL(5,2) DEFAULT 0,
                active_positions INTEGER DEFAULT 0,
                pending_orders INTEGER DEFAULT 0,
                last_trade_time TIMESTAMP,
                error_count INTEGER DEFAULT 0,
                metadata JSONB DEFAULT '{}'::jsonb
            )
        """)
        logger.info("[OK] System health table ready")
        
        # Create indexes for performance
        logger.info("[INFO] Creating performance indexes...")
        
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_positions_symbol ON positions(symbol)",
            "CREATE INDEX IF NOT EXISTS idx_positions_timestamp ON positions(timestamp DESC)",
            "CREATE INDEX IF NOT EXISTS idx_trades_position_id ON trades(position_id)",
            "CREATE INDEX IF NOT EXISTS idx_trades_timestamp ON trades(timestamp DESC)",
            "CREATE INDEX IF NOT EXISTS idx_account_balance_timestamp ON account_balance(timestamp DESC)",
            "CREATE INDEX IF NOT EXISTS idx_performance_timestamp ON performance_metrics(timestamp DESC)",
            "CREATE INDEX IF NOT EXISTS idx_system_health_timestamp ON system_health(timestamp DESC)"
        ]
        
        for index_sql in indexes:
            cursor.execute(index_sql)
        
        logger.info("[OK] All indexes created")
        
        # Insert initial data for testing
        logger.info("[INFO] Inserting initial test data...")
        
        # Insert initial account balance
        cursor.execute("""
            INSERT INTO account_balance (currency, available_balance, total_balance, metadata)
            VALUES ('USDT', 10000.00, 10000.00, '{"initial_balance": true, "test_mode": false}')
            ON CONFLICT DO NOTHING
        """)
        
        # Insert initial performance metrics
        cursor.execute("""
            INSERT INTO performance_metrics (total_pnl, daily_pnl, metadata)
            VALUES (0.00, 0.00, '{"system_start": true, "profit_target": 50.0}')
        """)
        
        # Insert initial system health
        cursor.execute("""
            INSERT INTO system_health (api_status, database_status, metadata)
            VALUES ('initializing', 'healthy', '{"startup": true, "profit_mode": "maximum"}')
        """)
        
        logger.info("[OK] Initial data inserted")
        
        # Close connection
        cursor.close()
        conn.close()
        
        print("\n" + "="*80)
        print("✅ SUCCESS: DATABASE SCHEMA FIXED FOR MAXIMUM PROFIT GENERATION")
        print("✅ ALL TABLES CREATED WITH PROPER SCHEMA")
        print("✅ FOREIGN KEY RELATIONSHIPS ESTABLISHED") 
        print("✅ PERFORMANCE INDEXES CREATED")
        print("✅ SYSTEM READY FOR BTC PROFIT GENERATION")
        print("="*80)
        
        return True
        
    except Exception as e:
        logger.error(f"[ERROR] Database schema fix failed: {e}")
        print("\n" + "="*80)
        print("❌ FAILED: DATABASE SCHEMA FIX")
        print(f"❌ ERROR: {e}")
        print("="*80)
        return False

if __name__ == "__main__":
    fix_database_schema()
