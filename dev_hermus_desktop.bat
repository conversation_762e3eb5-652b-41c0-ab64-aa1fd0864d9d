@echo off
echo ========================================
echo HERMUS Desktop - Development Mode
echo ========================================
echo.

REM Change to E: drive and project directory
E:
cd /d "E:\The_real_deal_copy\Bybit_Bot\BOT"

echo [1/3] Activating bybit-trader conda environment...
call conda activate bybit-trader
if errorlevel 1 (
    echo ERROR: Failed to activate bybit-trader environment
    pause
    exit /b 1
)

echo [2/3] Checking desktop application...
if not exist "desktop-app\package.json" (
    echo ERROR: Desktop application not found
    echo Please run setup_complete_desktop.bat first
    pause
    exit /b 1
)

echo [3/3] Starting Development Mode...
cd desktop-app

echo.
echo ========================================
echo Development Mode Features:
echo ========================================
echo - Hot Reload for React components
echo - DevTools enabled
echo - Vite development server
echo - Real-time code changes
echo.
echo Starting Vite dev server and Electron...
echo.

REM Set development environment variable
set ELECTRON_IS_DEV=true

REM Start development mode with concurrent processes
call npm run dev

echo.
echo Development session ended.
pause
