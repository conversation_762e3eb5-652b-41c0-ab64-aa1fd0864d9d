#!/usr/bin/env python3
"""
Test MetaCognitionEngine constructor fix
"""

def test_metacognition_engine_fix():
    try:
        print("Testing MetaCognitionEngine with keyword arguments...")
        
        from bybit_bot.ai.meta_cognition_engine import MetaCognitionEngine
        from bybit_bot.core.config import EnhancedBotConfig
        from bybit_bot.database.connection import DatabaseManager
        
        # Create test config and database manager
        config = EnhancedBotConfig()
        db_manager = DatabaseManager(config)
        
        # Test the MetaCognitionEngine constructor with keyword arguments
        engine = MetaCognitionEngine(config=config, database_manager=db_manager)
        
        print("✅ MetaCognitionEngine constructor fixed successfully!")
        print(f"✅ Engine config: {type(engine.config)}")
        print(f"✅ Engine db_manager: {type(engine.db_manager)}")
        return True
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_metacognition_engine_fix()
