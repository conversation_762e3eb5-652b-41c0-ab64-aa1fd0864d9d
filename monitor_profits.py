#!/usr/bin/env python3
"""
PROFIT MONITORING DASHBOARD
Real-time profit tracking and performance metrics
"""

import asyncio
import sqlite3
from datetime import datetime, timedelta

async def monitor_profits():
    """Monitor real-time profits"""
    print("[INFO] Starting profit monitoring...")
    
    try:
        conn = sqlite3.connect("bybit_trading_bot.db")
        cursor = conn.cursor()
        
        while True:
            # Get recent trades
            cursor.execute("""
                SELECT symbol, side, quantity, price, profit_loss, executed_at 
                FROM trades 
                WHERE executed_at >= datetime('now', '-1 hour')
                ORDER BY executed_at DESC
                LIMIT 10
            """)
            
            trades = cursor.fetchall()
            
            if trades:
                total_pnl = sum(trade[4] for trade in trades if trade[4])
                print(f"[{datetime.now().strftime('%H:%M:%S')}] Recent Trades: {len(trades)}, Total PnL: {total_pnl:.4f}")
                
                for trade in trades[:3]:  # Show last 3 trades
                    symbol, side, qty, price, pnl, executed_at = trade
                    pnl_str = f"{pnl:.4f}" if pnl else "0.0000"
                    print(f"  {symbol} {side} {qty} @ {price} | PnL: {pnl_str}")
            else:
                print(f"[{datetime.now().strftime('%H:%M:%S')}] No recent trades")
            
            await asyncio.sleep(30)  # Update every 30 seconds
            
    except Exception as e:
        print(f"[ERROR] Profit monitoring failed: {e}")
    finally:
        conn.close()

if __name__ == "__main__":
    asyncio.run(monitor_profits())
