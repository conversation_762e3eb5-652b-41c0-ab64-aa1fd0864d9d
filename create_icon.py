"""
Quick icon converter - Creates PNG from SVG for PWA icons
"""
try:
    from PIL import Image, ImageDraw, ImageFont
    import io
    
    # Create a 192x192 PNG icon manually
    def create_trading_bot_icon():
        # Create image with gradient background
        img = Image.new('RGBA', (192, 192), (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)
        
        # Background with rounded corners effect
        for i in range(192):
            for j in range(192):
                # Create rounded rectangle effect
                if (i < 32 and j < 32) or (i < 32 and j > 159) or (i > 159 and j < 32) or (i > 159 and j > 159):
                    # Corner radius calculation
                    if i < 32 and j < 32:
                        dist = ((i-32)**2 + (j-32)**2)**0.5
                        if dist > 32: continue
                    elif i < 32 and j > 159:
                        dist = ((i-32)**2 + (j-159)**2)**0.5
                        if dist > 32: continue
                    elif i > 159 and j < 32:
                        dist = ((i-159)**2 + (j-32)**2)**0.5
                        if dist > 32: continue
                    elif i > 159 and j > 159:
                        dist = ((i-159)**2 + (j-159)**2)**0.5
                        if dist > 32: continue
                
                # Gradient background
                t = (i + j) / 384.0
                r = int(0 + t * 255)
                g = int(255 - t * 51)
                b = int(136 + t * 68)
                img.putpixel((i, j), (r, g, b, 255))
        
        # Draw stylized head silhouette
        # Head outline
        draw.ellipse([56, 48, 136, 128], fill=(0, 0, 0, 200), outline=(45, 55, 72, 255), width=2)
        
        # Profile details
        draw.polygon([(88, 56), (112, 64), (112, 104), (88, 112), (83, 88), (88, 72)], fill=(45, 55, 72, 180))
        
        # Add $ symbol
        try:
            # Try to use a font if available
            font = ImageFont.truetype("arial.ttf", 24)
        except:
            # Use default font
            font = ImageFont.load_default()
        
        draw.text((96, 140), "$BOT", fill=(0, 212, 170, 255), font=font, anchor="mm")
        
        # Border accent
        draw.rectangle([4, 4, 187, 187], outline=(0, 212, 170, 128), width=2)
        
        return img
    
    # Create and save the icon
    icon = create_trading_bot_icon()
    icon.save('static/icon-192x192.png', 'PNG')
    print("[OK] Created static/icon-192x192.png")
    
    # Create smaller version for favicon
    icon_small = icon.resize((32, 32), Image.Resampling.LANCZOS)
    icon_small.save('static/favicon.png', 'PNG')
    print("[OK] Created static/favicon.png")
    
except ImportError:
    print("[INFO] PIL not available, using simple fallback method")
    # Create a simple text-based icon
    with open('static/icon-192x192.png', 'wb') as f:
        # This is a minimal 1x1 PNG, browsers will scale it
        f.write(b'\x89PNG\r\n\x1a\n\x00\x00\x00\rIHDR\x00\x00\x00\x01\x00\x00\x00\x01\x08\x02\x00\x00\x00\x90wS\xde\x00\x00\x00\x0cIDATx\x9cc```\x00\x00\x00\x04\x00\x01\x27\x10\xfc\x00\x00\x00\x00IEND\xaeB`\x82')
    print("[OK] Created fallback icon")
