#!/usr/bin/env python3
"""
Test script to verify OpenRouter is now optional
"""
import sys
import traceback

def test_openrouter_optional():
    """Test that OpenRouter is optional and system can run without it"""
    try:
        print("[OK] Starting OpenRouter optional test...")
        
        # Test 1: Import SuperGPT Integration
        print("[TESTING] Importing SuperGPT Integration...")
        from bybit_bot.ai.supergpt_integration import SuperGPTIntegration, OPENROUTER_AVAILABLE
        print(f"[OK] SuperGPT Integration imported successfully")
        print(f"[INFO] OpenRouter available: {OPENROUTER_AVAILABLE}")
        
        # Test 2: Import main system
        print("[TESTING] Importing main unified system...")
        import main_unified_system
        print("[OK] Main unified system imported successfully")
        
        # Test 3: Check configuration
        print("[TESTING] Loading configuration...")
        from bybit_bot.core.config import EnhancedBotConfig
        from bybit_bot.config.config_manager import ConfigManager
        config_manager = ConfigManager()
        config = config_manager.get_config()
        print(f"[OK] Configuration loaded successfully")
        print(f"[INFO] Config type: {type(config)}")
        
        print("[SUCCESS] All tests passed - OpenRouter is now optional!")
        return True
        
    except Exception as e:
        print(f"[ERROR] Test failed: {e}")
        print(f"[ERROR] Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = test_openrouter_optional()
    sys.exit(0 if success else 1)
