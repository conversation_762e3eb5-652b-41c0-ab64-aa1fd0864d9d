"""
Secure Credential Manager with AES-256 Encryption
Encrypts all credentials before storage and decrypts for use
"""

import os
import json
import base64
from typing import Dict, Any, Optional
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import logging

logger = logging.getLogger(__name__)

class SecureCredentialManager:
    """Manages encrypted credential storage and retrieval"""
    
    def __init__(self, master_password: Optional[str] = None):
        self.encrypted_file = "credentials.enc"
        self.salt_file = "credentials.salt"
        self._fernet = None
        self._setup_encryption(master_password)
    
    def _setup_encryption(self, master_password: Optional[str] = None):
        """Setup encryption with master password"""
        try:
            # Use environment variable or prompt for master password
            if not master_password:
                master_password = os.getenv('MASTER_PASSWORD', 'BYBIT_TRADING_MASTER_2025')
            
            # Generate or load salt
            if os.path.exists(self.salt_file):
                with open(self.salt_file, 'rb') as f:
                    salt = f.read()
            else:
                salt = os.urandom(16)
                with open(self.salt_file, 'wb') as f:
                    f.write(salt)
            
            # Derive key from password
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=salt,
                iterations=100000,
            )
            key = base64.urlsafe_b64encode(kdf.derive(master_password.encode()))
            self._fernet = Fernet(key)
            
            logger.info("[SECURE] Encryption system initialized")
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to setup encryption: {e}")
            raise
    
    def encrypt_and_save_credentials(self, credentials: Dict[str, Any]):
        """Encrypt and save credentials to file"""
        try:
            # Convert to JSON and encrypt
            json_data = json.dumps(credentials)
            encrypted_data = self._fernet.encrypt(json_data.encode())
            
            # Save encrypted data
            with open(self.encrypted_file, 'wb') as f:
                f.write(encrypted_data)
            
            logger.info("[SECURE] Credentials encrypted and saved")
            return True
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to encrypt credentials: {e}")
            return False
    
    def load_and_decrypt_credentials(self) -> Dict[str, Any]:
        """Load and decrypt credentials from file"""
        try:
            if not os.path.exists(self.encrypted_file):
                logger.warning("[WARNING] No encrypted credentials file found")
                return {}
            
            # Read and decrypt
            with open(self.encrypted_file, 'rb') as f:
                encrypted_data = f.read()
            
            decrypted_data = self._fernet.decrypt(encrypted_data)
            credentials = json.loads(decrypted_data.decode())
            
            logger.info("[SECURE] Credentials decrypted successfully")
            return credentials
            
        except Exception as e:
            logger.error(f"[ERROR] Failed to decrypt credentials: {e}")
            return {}
    
    def add_credential(self, key: str, value: str):
        """Add or update a single credential"""
        credentials = self.load_and_decrypt_credentials()
        credentials[key] = value
        return self.encrypt_and_save_credentials(credentials)
    
    def get_credential(self, key: str) -> Optional[str]:
        """Get a single credential"""
        credentials = self.load_and_decrypt_credentials()
        return credentials.get(key)
    
    def remove_credential(self, key: str):
        """Remove a credential"""
        credentials = self.load_and_decrypt_credentials()
        if key in credentials:
            del credentials[key]
            return self.encrypt_and_save_credentials(credentials)
        return False

def setup_secure_credentials():
    """Setup all credentials with encryption"""
    logger.info("[SECURE] Setting up encrypted credential storage...")
    
    manager = SecureCredentialManager()
    
    # Bybit API credentials
    credentials = {
        # Bybit Trading
        'BYBIT_API_KEY': 'WbQDRvmESPfUGgXQEj',
        'BYBIT_API_SECRET': 'vdvi3Q34C7m65rHuzFw3I9kbGeyGr4oMFUga',
        'BYBIT_TESTNET': 'false',
        
        # Twitter/X API credentials
        'X_ACCESS_TOKEN': '1217140753241190406-8InYduLXbHKzySeUhoVOaWsOfBTqqX',
        'X_ACCESS_TOKEN_SECRET': '43ZAuBA8otxg6V6xQQ4dwYJQe2j24Wiq4Iw7li7G1bm7Y',
        'X_API_KEY': '*************************',
        'X_API_SECRET': '090BT2ShtA0sNEaNZJmOKPA0nruelXEdfNXHRkWZsv9BphWL0g',
        'X_BEARER_TOKEN': 'AAAAAAAAAAAAAAAAAAAAACPo1AEAAAAA%2FkHMV0BXzYlGb9U3N2MwD5gfnAU%3D56Ql9TOVDAQxhJVAUuYX6TOzBUwjok1niLnHRHnkpmqbnQu6pW',
        
        # Additional API keys for maximum data collection
        'NEWSAPI_KEY': '',  # Will be added when needed
        'REDDIT_CLIENT_ID': '',  # Will be added when needed
        'REDDIT_CLIENT_SECRET': '',  # Will be added when needed
        'FRED_API_KEY': '',  # Will be added when needed
        'OPENROUTER_API_KEY': '',  # Will be added when needed
    }
    
    # Encrypt and save
    if manager.encrypt_and_save_credentials(credentials):
        logger.info("[SECURE] All credentials encrypted and stored securely")
        return True
    else:
        logger.error("[ERROR] Failed to setup secure credentials")
        return False

def get_decrypted_credential(key: str) -> Optional[str]:
    """Get a decrypted credential by key"""
    manager = SecureCredentialManager()
    return manager.get_credential(key)

def get_all_decrypted_credentials() -> Dict[str, Any]:
    """Get all decrypted credentials"""
    manager = SecureCredentialManager()
    return manager.load_and_decrypt_credentials()

if __name__ == "__main__":
    # Setup secure credentials
    setup_secure_credentials()
    print("[SECURE] Credential encryption system setup complete")
