#!/usr/bin/env python3
"""
Mobile App Logo Verification Script
Tests that the logo integration is working correctly
"""

import requests
from pathlib import Path

def test_mobile_app_logo():
    """Test mobile app with logo integration"""
    print("=" * 60)
    print("MOBILE APP LOGO INTEGRATION TEST")
    print("=" * 60)
    
    # Check if logo files exist
    print("\n[1/5] Checking logo files...")
    logo_files = [
        "static/logo.svg",
        "static/icon-192x192.png", 
        "static/favicon.png"
    ]
    
    for file_path in logo_files:
        if Path(file_path).exists():
            print(f"   ✅ {file_path} exists")
        else:
            print(f"   ❌ {file_path} missing")
    
    # Test server endpoints
    print("\n[2/5] Testing server endpoints...")
    base_url = "http://localhost:8000"
    endpoints = [
        "/status",
        "/mobile", 
        "/manifest.json",
        "/static/logo.svg"
    ]
    
    for endpoint in endpoints:
        try:
            response = requests.get(f"{base_url}{endpoint}", timeout=5)
            if response.status_code == 200:
                print(f"   ✅ {endpoint} - Status: {response.status_code}")
            else:
                print(f"   ⚠️  {endpoint} - Status: {response.status_code}")
        except requests.exceptions.RequestException as e:
            print(f"   ❌ {endpoint} - Error: Connection failed")
    
    # Test mobile app content
    print("\n[3/5] Testing mobile app HTML content...")
    try:
        response = requests.get(f"{base_url}/mobile", timeout=5)
        if response.status_code == 200:
            html_content = response.text
            
            # Check for logo elements
            logo_checks = [
                ('app-logo', 'Logo CSS class'),
                ('logo.svg', 'Logo SVG reference'),
                ('header-content', 'Header structure'),
                ('Trading Bot', 'App title')
            ]
            
            for check, description in logo_checks:
                if check in html_content:
                    print(f"   ✅ {description} found")
                else:
                    print(f"   ❌ {description} missing")
        else:
            print(f"   ❌ Mobile app returned status: {response.status_code}")
    except requests.exceptions.RequestException:
        print("   ❌ Could not fetch mobile app content")
    
    # Test PWA manifest
    print("\n[4/5] Testing PWA manifest...")
    try:
        response = requests.get(f"{base_url}/manifest.json", timeout=5)
        if response.status_code == 200:
            manifest = response.json()
            if 'icons' in manifest and len(manifest['icons']) > 0:
                print(f"   ✅ PWA manifest has {len(manifest['icons'])} icons")
                for icon in manifest['icons']:
                    print(f"      - {icon.get('src', 'unknown')} ({icon.get('sizes', 'unknown')})")
            else:
                print("   ⚠️  PWA manifest has no icons")
        else:
            print(f"   ❌ Manifest returned status: {response.status_code}")
    except requests.exceptions.RequestException:
        print("   ❌ Could not fetch PWA manifest")
    
    # Network accessibility test
    print("\n[5/5] Network accessibility info...")
    print("   📱 Mobile Access URLs:")
    print("      - Local: http://localhost:8000/mobile")
    print("      - Network: http://**************:8000/mobile")
    print("   📲 Installation: Add to Home Screen in mobile browser")
    print("   🎨 Logo: Your stylized portrait will appear in the header")
    
    print("\n" + "=" * 60)
    print("LOGO INTEGRATION TEST COMPLETE")
    print("=" * 60)
    print("\n🚀 Ready to access your branded mobile trading app!")

if __name__ == "__main__":
    test_mobile_app_logo()
