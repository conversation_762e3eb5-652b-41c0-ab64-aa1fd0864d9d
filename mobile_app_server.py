#!/usr/bin/env python3
"""
STANDALONE MOBILE APP SERVER
This server serves ONLY the mobile app installation and interface.
It works independently of the trading system for reliable mobile access.
"""

from fastapi import FastAPI
from fastapi.responses import HTMLResponse, JSONResponse
import uvicorn
from pathlib import Path

# Create FastAPI app for mobile-only server
mobile_app = FastAPI(
    title="Bybit Trading Bot - Mobile App Server",
    description="Standalone mobile app server - works independently of trading system",
    version="1.0.0"
)

# Get current directory
current_dir = Path(__file__).parent

def load_mobile_app_html() -> str:
    """Load the mobile control app HTML"""
    try:
        with open(current_dir / "mobile_control_app.html", "r", encoding="utf-8") as f:
            return f.read()
    except FileNotFoundError:
        return """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mobile Trading App</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
            background: linear-gradient(135deg, #0f0f23, #1a1a2e, #16213e);
            color: #fff; min-height: 100vh; display: flex; align-items: center; justify-content: center;
        }
        .container { text-align: center; padding: 40px; }
        h1 { color: #00ff88; font-size: 2rem; margin-bottom: 20px; }
        p { color: #ccc; margin-bottom: 30px; }
        .btn { 
            background: linear-gradient(45deg, #00ff88, #00ccff); 
            color: #000; padding: 15px 30px; border: none; border-radius: 25px;
            font-weight: 700; text-decoration: none; display: inline-block; margin: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📱 Mobile Trading App</h1>
        <p>Mobile control interface is loading...</p>
        <a href="/" class="btn">Return to Main</a>
    </div>
</body>
</html>
        """

def load_installation_guide_html() -> str:
    """Load the installation guide HTML"""
    try:
        with open(current_dir / "permanent_mobile_app_guide.html", "r", encoding="utf-8") as f:
            return f.read()
    except FileNotFoundError:
        return """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mobile App Installation</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
            background: linear-gradient(135deg, #0f0f23, #1a1a2e, #16213e);
            color: #fff; padding: 20px;
        }
        .container { max-width: 600px; margin: 0 auto; text-align: center; }
        h1 { color: #00ff88; font-size: 2rem; margin-bottom: 20px; }
        .install-btn { 
            background: linear-gradient(45deg, #00ff88, #00ccff); 
            color: #000; padding: 15px 30px; border: none; border-radius: 25px;
            font-weight: 700; text-decoration: none; display: inline-block; margin: 20px;
        }
        .steps { text-align: left; margin: 30px 0; }
        .steps li { margin: 10px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>📱 Install Mobile Trading App</h1>
        <p>Follow these steps to install the permanent mobile app:</p>
        
        <ol class="steps">
            <li>Open this page on your mobile browser</li>
            <li>Add to Home Screen (iPhone: Share → Add to Home Screen)</li>
            <li>Launch from home screen icon</li>
            <li>Control your trading system with one-click buttons</li>
        </ol>
        
        <a href="/mobile" class="install-btn">📱 Open Mobile App</a>
        <a href="/control" class="install-btn">🎮 Control Panel</a>
    </div>
</body>
</html>
        """

# Root endpoint
@mobile_app.get("/", response_class=HTMLResponse)
async def root():
    """Root endpoint - redirect to mobile app"""
    return HTMLResponse(content="""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="theme-color" content="#000000">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <title>Bybit Trading Bot - Mobile Access</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { 
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
            background: linear-gradient(135deg, #0f0f23, #1a1a2e, #16213e);
            color: #fff; min-height: 100vh; display: flex; align-items: center; justify-content: center;
        }
        .container { text-align: center; padding: 40px; max-width: 500px; }
        h1 { color: #00ff88; font-size: 2.5rem; margin-bottom: 20px; }
        .subtitle { color: #ccc; margin-bottom: 40px; font-size: 1.2rem; }
        .btn-grid { display: grid; gap: 20px; margin: 30px 0; }
        .btn { 
            background: linear-gradient(45deg, #00ff88, #00ccff); 
            color: #000; padding: 20px 30px; border: none; border-radius: 25px;
            font-weight: 700; text-decoration: none; display: block;
            font-size: 18px; text-transform: uppercase; letter-spacing: 1px;
            transition: all 0.3s ease;
        }
        .btn:hover { transform: translateY(-2px); box-shadow: 0 10px 20px rgba(0, 255, 136, 0.3); }
        .btn.secondary { background: linear-gradient(45deg, #ffaa00, #ff8800); }
        .status { 
            background: rgba(255, 255, 255, 0.1); border-radius: 15px; padding: 20px; 
            margin: 30px 0; border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .status-indicator { 
            display: inline-block; width: 12px; height: 12px; 
            border-radius: 50%; margin-right: 10px;
            background: #ffaa00; box-shadow: 0 0 10px rgba(255, 170, 0, 0.5);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>📱 Mobile Trading Control</h1>
        <p class="subtitle">Standalone Mobile App Server</p>
        
        <div class="status">
            <span class="status-indicator"></span>
            Mobile App Server: Active
        </div>
        
        <div class="btn-grid">
            <a href="/mobile" class="btn">📱 Launch Mobile App</a>
            <a href="/control" class="btn">🎮 Control Panel</a>
            <a href="/install-guide" class="btn secondary">📋 Installation Guide</a>
        </div>
        
        <p style="color: #888; margin-top: 30px;">
            This mobile server works independently of the trading system.<br>
            Install the mobile app for permanent access to system controls.
        </p>
    </div>
</body>
</html>
    """)

# Mobile app endpoint
@mobile_app.get("/mobile", response_class=HTMLResponse)
async def mobile_control_app():
    """Serve the mobile control app"""
    return HTMLResponse(content=load_mobile_app_html())

# Control panel endpoint
@mobile_app.get("/control", response_class=HTMLResponse)
async def control_panel():
    """Serve the control panel"""
    return HTMLResponse(content=load_mobile_app_html())

# Installation guide
@mobile_app.get("/install-guide", response_class=HTMLResponse)
async def installation_guide():
    """Serve the installation guide"""
    return HTMLResponse(content=load_installation_guide_html())

# PWA Manifest
@mobile_app.get("/manifest.json")
async def get_manifest():
    """PWA manifest for mobile installation"""
    manifest = {
        "name": "Bybit Trading Bot",
        "short_name": "Trading Bot",
        "description": "Mobile control for Bybit Trading Bot",
        "start_url": "/mobile",
        "display": "standalone",
        "background_color": "#0f0f23",
        "theme_color": "#00ff88",
        "orientation": "portrait",
        "scope": "/",
        "icons": [
            {
                "src": "/static/icon-192x192.png",
                "sizes": "192x192",
                "type": "image/png",
                "purpose": "any maskable"
            },
            {
                "src": "/static/favicon.png",
                "sizes": "32x32",
                "type": "image/png"
            },
            {
                "src": "/static/logo.svg",
                "sizes": "120x120",
                "type": "image/svg+xml"
            }
        ],
        "shortcuts": [
            {
                "name": "Start Trading",
                "url": "/mobile?action=start",
                "description": "Start the trading system"
            },
            {
                "name": "Stop Trading", 
                "url": "/mobile?action=stop",
                "description": "Stop the trading system"
            },
            {
                "name": "Emergency Stop",
                "url": "/mobile?action=emergency",
                "description": "Emergency stop all trading"
            }
        ]
    }
    return JSONResponse(content=manifest)

# Service Worker
@mobile_app.get("/service-worker.js")
async def get_service_worker():
    """Service worker for PWA functionality"""
    sw_content = """
// Service Worker for Bybit Trading Bot Mobile App
const CACHE_NAME = 'bybit-trading-bot-v1';
const urlsToCache = [
    '/',
    '/mobile', 
    '/control',
    '/install-guide',
    '/manifest.json'
];

// Install event
self.addEventListener('install', function(event) {
    event.waitUntil(
        caches.open(CACHE_NAME)
            .then(function(cache) {
                return cache.addAll(urlsToCache);
            })
    );
});

// Fetch event
self.addEventListener('fetch', function(event) {
    event.respondWith(
        caches.match(event.request)
            .then(function(response) {
                // Return cached version or fetch from network
                return response || fetch(event.request);
            }
        )
    );
});

// Background sync for offline functionality
self.addEventListener('sync', function(event) {
    if (event.tag === 'background-sync') {
        event.waitUntil(doBackgroundSync());
    }
});

function doBackgroundSync() {
    // Sync data when connection is restored
    return fetch('/api/sync')
        .then(response => response.json())
        .catch(err => console.log('Background sync failed:', err));
}
    """
    return HTMLResponse(content=sw_content, media_type="application/javascript")

# API endpoints that work without trading system
@mobile_app.get("/status")
async def get_status():
    """Get mobile server status"""
    return {
        "mobile_server": "active",
        "trading_system": "unknown", 
        "message": "Mobile app server is running independently",
        "timestamp": "2025-07-17T10:30:00Z",
        "mobile_features": ["app_installation", "control_interface", "offline_mode"]
    }

@mobile_app.get("/api/dashboard")
async def get_dashboard():
    """Get dashboard data (mock when trading system offline)"""
    return {
        "status": "mobile_server_only",
        "message": "Connect to trading system for live data",
        "mobile_server": "active",
        "features_available": [
            "Mobile app installation",
            "Control interface", 
            "Offline functionality",
            "PWA support"
        ]
    }

# System control endpoints (will forward to trading system when available)
@mobile_app.post("/system/start")
async def start_system():
    """Start system endpoint"""
    return {
        "status": "mobile_server_only",
        "message": "Connect to trading system to start",
        "action": "start_requested"
    }

@mobile_app.post("/system/stop")
async def stop_system():
    """Stop system endpoint"""
    return {
        "status": "mobile_server_only", 
        "message": "Connect to trading system to stop",
        "action": "stop_requested"
    }

@mobile_app.post("/system/restart")
async def restart_system():
    """Restart system endpoint"""
    return {
        "status": "mobile_server_only",
        "message": "Connect to trading system to restart", 
        "action": "restart_requested"
    }

@mobile_app.post("/api/trading/emergency-stop")
async def emergency_stop():
    """Emergency stop endpoint"""
    return {
        "status": "mobile_server_only",
        "message": "Connect to trading system for emergency stop",
        "action": "emergency_stop_requested"
    }

# Health check
@mobile_app.get("/health")
async def health_check():
    """Health check for mobile server"""
    return {
        "status": "healthy",
        "service": "mobile_app_server", 
        "independent_operation": True,
        "timestamp": "2025-07-17T10:30:00Z"
    }

def run_mobile_server(host: str = "0.0.0.0", port: int = 8000):
    """Run the standalone mobile app server"""
    print("=" * 60)
    print("STANDALONE MOBILE APP SERVER")
    print("=" * 60)
    print(f"[MOBILE] Server starting on http://{host}:{port}")
    print(f"[MOBILE] Mobile app: http://{host}:{port}/mobile")
    print(f"[MOBILE] Control panel: http://{host}:{port}/control") 
    print(f"[MOBILE] Install guide: http://{host}:{port}/install-guide")
    print("[MOBILE] This server works independently of trading system")
    print("[MOBILE] Mobile app installation available even when trading is offline")
    print("=" * 60)
    
    uvicorn.run(mobile_app, host=host, port=port, log_level="info")

if __name__ == "__main__":
    """
    STANDALONE MOBILE APP SERVER
    - Works independently of trading system
    - Serves mobile app installation
    - Provides control interface
    - PWA support for permanent installation
    """
    run_mobile_server()
