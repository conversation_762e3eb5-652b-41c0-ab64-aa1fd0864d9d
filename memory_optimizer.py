
import gc
import psutil
import asyncio

class MemoryOptimizer:
    def __init__(self):
        self.max_memory_percent = 80.0
        
    async def optimize_memory(self):
        '''Optimize memory usage'''
        try:
            # Force garbage collection
            gc.collect()
            
            # Get current memory usage
            memory = psutil.virtual_memory()
            if memory.percent > self.max_memory_percent:
                print(f"[WARNING] High memory usage: {memory.percent:.1f}%")
                
                # Force aggressive garbage collection
                for _ in range(3):
                    gc.collect()
                    await asyncio.sleep(0.1)
                
                # Clear caches if available
                try:
                    import torch
                    if torch.cuda.is_available():
                        torch.cuda.empty_cache()
                except ImportError:
                    pass
                
                print("[OK] Memory optimization completed")
            
        except Exception as e:
            print(f"[ERROR] Memory optimization failed: {e}")

# Create global memory optimizer instance
memory_optimizer = MemoryOptimizer()
