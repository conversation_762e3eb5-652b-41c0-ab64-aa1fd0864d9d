@echo off
REM STANDALONE MOBILE APP SERVER
REM This starts ONLY the mobile app server - no trading system required
REM Mobile app installation works independently

title Bybit Trading Bot - Mobile App Server (Standalone)

echo ================================================================
echo STANDALONE MOBILE APP SERVER
echo ================================================================
echo [MOBILE] Starting mobile app server only
echo [MOBILE] No trading system required
echo [MOBILE] Mobile app installation works independently
echo [MOBILE] Access at: http://*************:8000/mobile
echo ================================================================

REM Change to script directory
cd /d "%~dp0"

REM Activate conda environment
echo [MOBILE] Activating environment...
call E:\conda\scripts\activate_bybit_trader.bat

REM Check activation
if %ERRORLEVEL% NEQ 0 (
    echo [WARNING] Environment activation failed - trying python directly
    set "PATH=E:\conda\miniconda3\Scripts;%PATH%"
)

REM Show info
echo [MOBILE] Current directory: %CD%
echo [MOBILE] Starting standalone mobile server...

REM Start mobile server only
python mobile_app_server.py

echo [MOBILE] Mobile server stopped
pause
