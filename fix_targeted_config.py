#!/usr/bin/env python3
"""
Targeted fix for specific configuration parameter issues
Only fixes the exact parameters causing TypeError
"""
import sys
import traceback
from pathlib import Path

def fix_specific_parameter_issues():
    """Fix only the specific parameter mismatches causing errors"""
    config_file = Path("bybit_bot/core/config.py")
    
    print("[INFO] Fixing specific parameter issues...")
    
    try:
        # Read the current config file
        with open(config_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 1. Fix EnhancedTradingConfig - add missing parameters
        if "class EnhancedTradingConfig:" in content:
            # Add the specific missing parameters
            additions = [
                "    stop_loss_pct: float = 0.02",
                "    take_profit_pct: float = 0.10",
                "    max_daily_trades: int = 100"
            ]
            
            # Find the end of EnhancedTradingConfig class
            lines = content.split('\n')
            for i, line in enumerate(lines):
                if "class EnhancedTradingConfig:" in line:
                    # Find the end of this class (next class or end of file)
                    for j in range(i + 1, len(lines)):
                        if lines[j].startswith("class ") or lines[j].startswith("@dataclass"):
                            # Insert before this line
                            for k, addition in enumerate(additions):
                                if addition.split(':')[0].strip() not in content:
                                    lines.insert(j + k, addition)
                            break
                    break
            content = '\n'.join(lines)
        
        # 2. Fix EnhancedDatabaseConfig - add path parameter
        if "class EnhancedDatabaseConfig:" in content:
            if "path: str" not in content:
                content = content.replace(
                    "timeseries: Dict[str, Any] = field(default_factory=dict)",
                    "timeseries: Dict[str, Any] = field(default_factory=dict)\n    path: str = \"\""
                )
        
        # 3. Make all Enhanced config classes accept **kwargs
        enhanced_classes = [
            "EnhancedTradingConfig",
            "EnhancedDatabaseConfig", 
            "EnhancedAPIKeysConfig",
            "EnhancedAIConfig"
        ]
        
        for class_name in enhanced_classes:
            if f"class {class_name}:" in content:
                # Check if it already has flexible init
                class_start = content.find(f"class {class_name}:")
                if class_start != -1:
                    # Find the next class or end of file
                    next_class = content.find("\nclass ", class_start + 1)
                    next_dataclass = content.find("\n@dataclass", class_start + 1)
                    
                    class_end = len(content)
                    if next_class != -1:
                        class_end = min(class_end, next_class)
                    if next_dataclass != -1:
                        class_end = min(class_end, next_dataclass)
                    
                    class_content = content[class_start:class_end]
                    
                    # If no __init__ method, add one that accepts **kwargs
                    if "def __init__" not in class_content:
                        init_method = f'''
    
    def __init__(self, **kwargs):
        \"\"\"Initialize {class_name} with flexible parameter handling\"\"\"
        # Set any provided parameters as attributes
        for key, value in kwargs.items():
            if hasattr(self, key) or not hasattr(self, key):
                setattr(self, key, value)'''
                        
                        # Insert before the next class
                        content = content[:class_end] + init_method + content[class_end:]
        
        # Write the fixed content back
        with open(config_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("[OK] Fixed specific parameter issues")
        return True
        
    except Exception as e:
        print(f"[ERROR] Failed to fix parameter issues: {e}")
        traceback.print_exc()
        return False

def test_config_loading():
    """Test configuration loading"""
    try:
        from bybit_bot.core.config import get_config
        
        print("[INFO] Testing configuration loading...")
        config = get_config()
        
        print(f"[OK] Configuration loaded successfully")
        return True
        
    except Exception as e:
        print(f"[ERROR] Configuration loading failed: {e}")
        print(f"[ERROR] Error type: {type(e)}")
        traceback.print_exc()
        return False

def main():
    """Main execution"""
    print("=" * 60)
    print("TARGETED CONFIGURATION PARAMETER FIX")
    print("=" * 60)
    
    # Fix specific parameter issues
    if not fix_specific_parameter_issues():
        print("[CRITICAL] Failed to fix parameter issues!")
        sys.exit(1)
    
    # Test configuration loading
    if not test_config_loading():
        print("[WARNING] Configuration loading still has issues, but attempting system launch...")
    else:
        print("[SUCCESS] Configuration loading successful!")
    
    print("\n" + "=" * 60)
    print("[INFO] PARAMETER FIXES COMPLETED")
    print("Attempting system launch...")
    print("=" * 60)

if __name__ == "__main__":
    main()
