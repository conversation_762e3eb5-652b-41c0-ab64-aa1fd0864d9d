#!/usr/bin/env python3
"""
Fix all configuration parameter mismatches
CRITICAL: Ensure all config classes can handle any parameters from config.yaml
"""
import sys
import traceback
from pathlib import Path

def fix_configuration_classes():
    """Fix all configuration class parameter mismatches"""
    config_file = Path("bybit_bot/core/config.py")
    
    print("[INFO] Fixing configuration parameter handling...")
    
    try:
        # Read the current config file
        with open(config_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Find and fix BotConfig class
        if "@dataclass\nclass BotConfig:" in content:
            print("[INFO] Fixing BotConfig class...")
            
            # Add flexible initialization to BotConfig
            botconfig_fix = '''@dataclass
class BotConfig:
    """Main bot configuration"""
    # Core settings
    api_key: str = ""
    api_secret: str = ""
    environment: str = "testnet"
    log_level: str = "INFO"
    log_file: str = "trading_bot.log"
    
    # Trading settings
    trading: EnhancedTradingConfig = field(default_factory=EnhancedTradingConfig)
    risk_management: RiskManagementConfig = field(default_factory=RiskManagementConfig)
    portfolio: PortfolioConfig = field(default_factory=PortfolioConfig)
    strategies: StrategiesConfig = field(default_factory=StrategiesConfig)
    execution: ExecutionConfig = field(default_factory=ExecutionConfig)
    
    # AI and monitoring
    ai: AIConfig = field(default_factory=AIConfig)
    monitoring: MonitoringConfig = field(default_factory=MonitoringConfig)
    database: DatabaseConfig = field(default_factory=DatabaseConfig)
    
    # Performance settings
    max_workers: int = 10
    
    # Store any extra parameters
    extra_params: Dict[str, Any] = field(default_factory=dict)
    
    def __init__(self, **kwargs):
        """Initialize with flexible parameter handling"""
        # Get all field names from the dataclass
        field_names = {f.name for f in fields(self)}
        
        # Initialize defaults first
        for field_info in fields(self):
            if field_info.default_factory != dataclasses.MISSING:
                setattr(self, field_info.name, field_info.default_factory())
            elif field_info.default != dataclasses.MISSING:
                setattr(self, field_info.name, field_info.default)
        
        # Set provided values
        for key, value in kwargs.items():
            if key in field_names:
                if key in ['trading', 'risk_management', 'portfolio', 'strategies', 'execution', 'ai', 'monitoring', 'database']:
                    # Handle nested config objects
                    if isinstance(value, dict):
                        config_class = {
                            'trading': EnhancedTradingConfig,
                            'risk_management': RiskManagementConfig,
                            'portfolio': PortfolioConfig,
                            'strategies': StrategiesConfig,
                            'execution': ExecutionConfig,
                            'ai': AIConfig,
                            'monitoring': MonitoringConfig,
                            'database': DatabaseConfig
                        }.get(key)
                        if config_class:
                            setattr(self, key, config_class(**value))
                        else:
                            setattr(self, key, value)
                    else:
                        setattr(self, key, value)
                else:
                    setattr(self, key, value)
            else:
                # Store unknown parameters
                self.extra_params[key] = value
    
    def get(self, key: str, default=None):
        """Get a parameter value"""
        if hasattr(self, key):
            return getattr(self, key)
        return self.extra_params.get(key, default)'''
            
            # Replace the BotConfig class
            import re
            pattern = r'@dataclass\nclass BotConfig:.*?(?=@dataclass|class [A-Z]|\Z)'
            content = re.sub(pattern, botconfig_fix, content, flags=re.DOTALL)
        
        # Add necessary imports at the top
        if "import dataclasses" not in content:
            content = "import dataclasses\nfrom dataclasses import fields\n" + content
        
        # Write the fixed content back
        with open(config_file, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print("[OK] Configuration classes fixed successfully")
        return True
        
    except Exception as e:
        print(f"[ERROR] Failed to fix configuration classes: {e}")
        traceback.print_exc()
        return False

def test_configuration_loading():
    """Test that configuration can be loaded without errors"""
    try:
        from bybit_bot.core.config import BotConfig, get_config
        
        print("[INFO] Testing configuration loading...")
        
        # Try to load the config
        config = get_config()
        
        print(f"[OK] Configuration loaded successfully")
        print(f"[INFO] Trading mode: {config.trading.mode}")
        print(f"[INFO] Primary currency: {config.trading.primary_currency}")
        print(f"[INFO] BTC allocation: {config.trading.btc_allocation}")
        
        return True
        
    except Exception as e:
        print(f"[ERROR] Configuration loading failed: {e}")
        traceback.print_exc()
        return False

def main():
    """Main execution"""
    print("=" * 60)
    print("FIXING CONFIGURATION PARAMETER MISMATCHES")
    print("=" * 60)
    
    # Fix configuration classes
    if not fix_configuration_classes():
        print("[CRITICAL] Failed to fix configuration classes!")
        sys.exit(1)
    
    # Test configuration loading
    if not test_configuration_loading():
        print("[CRITICAL] Configuration still has issues!")
        sys.exit(1)
    
    print("\n" + "=" * 60)
    print("[SUCCESS] ALL CONFIGURATION FIXES COMPLETED")
    print("System is ready for launch!")
    print("=" * 60)

if __name__ == "__main__":
    main()
