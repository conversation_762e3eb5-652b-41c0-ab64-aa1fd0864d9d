@echo off
echo ========================================
echo HERMUS Frontend Component Sync
echo ========================================
echo.

REM Change to E: drive and project directory
E:
cd /d "E:\The_real_deal_copy\Bybit_Bot\BOT"

echo [1/4] Checking source and destination directories...
if not exist "gui\components" (
    echo ERROR: Source gui\components directory not found
    pause
    exit /b 1
)

if not exist "desktop-app\src\components" (
    echo Creating desktop-app\src\components directory...
    mkdir "desktop-app\src\components"
)

echo [2/4] Backing up existing desktop components...
if exist "desktop-app\src\components\*.jsx" (
    if not exist "desktop-app\src\components\backup" mkdir "desktop-app\src\components\backup"
    copy "desktop-app\src\components\*.jsx" "desktop-app\src\components\backup\" > nul 2>&1
    echo Backup created in desktop-app\src\components\backup\
)

echo [3/4] Converting and copying components from gui\components...
echo.

REM Copy TradingChart component (already converted)
if exist "gui\components\TradingChart.js" (
    echo - TradingChart.js already converted to TradingChart.jsx
)

REM Check for other components to copy
for %%f in (gui\components\*.js) do (
    echo Found component: %%~nxf
    echo - Manual conversion required for Material-UI compatibility
)

echo [4/4] Copying pages and utilities...
if exist "gui\pages" (
    echo Checking gui\pages for additional components...
    for /r "gui\pages" %%f in (*.js) do (
        echo Found page component: %%~nxf
    )
)

if exist "gui\utils" (
    echo Copying utility files...
    if not exist "desktop-app\src\utils" mkdir "desktop-app\src\utils"
    copy "gui\utils\*.js" "desktop-app\src\utils\" > nul 2>&1
    echo Utilities copied to desktop-app\src\utils\
)

echo.
echo ========================================
echo Component Sync Summary:
echo ========================================
echo - TradingChart: Converted to Material-UI
echo - TradingDashboard: Created for desktop
echo - ElectronContext: Desktop-specific context
echo.
echo NOTE: Manual conversion may be required for:
echo - Next.js specific components
echo - Custom CSS modules
echo - API service integrations
echo.
echo Sync completed!
pause
