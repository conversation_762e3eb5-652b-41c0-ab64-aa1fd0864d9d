import React, { createContext, useContext, useEffect, useState } from 'react';
import { toast } from 'react-toastify';

const ElectronContext = createContext();

export const useElectron = () => {
  const context = useContext(ElectronContext);
  if (!context) {
    throw new Error('useElectron must be used within an ElectronProvider');
  }
  return context;
};

export const ElectronProvider = ({ children }) => {
  const [isElectron, setIsElectron] = useState(false);
  const [tradingStatus, setTradingStatus] = useState({
    isTrading: false,
    processId: null,
    lastUpdate: null
  });
  const [logs, setLogs] = useState([]);
  const [isOnline, setIsOnline] = useState(true);

  useEffect(() => {
    // Check if running in Electron
    if (window.electronAPI) {
      setIsElectron(true);
      
      // Set up event listeners
      window.electronAPI.onTradingStatus((data) => {
        setTradingStatus({
          ...data,
          lastUpdate: new Date().toISOString()
        });
        
        if (data.emergency) {
          toast.error('EMERGENCY STOP ACTIVATED!');
        } else if (data.isTrading) {
          toast.success('Trading started successfully');
        } else {
          toast.info('Trading stopped');
        }
      });

      window.electronAPI.onTradingLog((logData) => {
        const timestamp = new Date().toISOString();
        setLogs(prev => [...prev.slice(-99), { // Keep last 100 logs
          timestamp,
          type: 'info',
          message: logData
        }]);
      });

      window.electronAPI.onTradingError((errorData) => {
        const timestamp = new Date().toISOString();
        setLogs(prev => [...prev.slice(-99), {
          timestamp,
          type: 'error',
          message: errorData
        }]);
        toast.error('Trading error occurred');
      });

      // Get initial trading status
      window.electronAPI.getTradingStatus().then(status => {
        setTradingStatus({
          ...status,
          lastUpdate: new Date().toISOString()
        });
      });
    }

    // Monitor online status
    const handleOnline = () => setIsOnline(true);
    const handleOffline = () => setIsOnline(false);
    
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
      
      if (window.electronAPI) {
        window.electronAPI.removeAllListeners('trading-status');
        window.electronAPI.removeAllListeners('trading-log');
        window.electronAPI.removeAllListeners('trading-error');
      }
    };
  }, []);

  const startTrading = async () => {
    if (!isElectron) {
      toast.error('Trading controls only available in desktop app');
      return;
    }
    
    try {
      await window.electronAPI.startTrading();
    } catch (error) {
      toast.error('Failed to start trading: ' + error.message);
    }
  };

  const stopTrading = async () => {
    if (!isElectron) {
      toast.error('Trading controls only available in desktop app');
      return;
    }
    
    try {
      await window.electronAPI.stopTrading();
    } catch (error) {
      toast.error('Failed to stop trading: ' + error.message);
    }
  };

  const emergencyStop = async () => {
    if (!isElectron) {
      toast.error('Emergency stop only available in desktop app');
      return;
    }
    
    try {
      await window.electronAPI.emergencyStop();
      toast.warning('Emergency stop initiated');
    } catch (error) {
      toast.error('Failed to execute emergency stop: ' + error.message);
    }
  };

  const clearLogs = () => {
    setLogs([]);
  };

  const value = {
    isElectron,
    tradingStatus,
    logs,
    isOnline,
    startTrading,
    stopTrading,
    emergencyStop,
    clearLogs
  };

  return (
    <ElectronContext.Provider value={value}>
      {children}
    </ElectronContext.Provider>
  );
};
