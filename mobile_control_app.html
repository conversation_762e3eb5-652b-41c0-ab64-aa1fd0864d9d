<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="theme-color" content="#000000">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="apple-mobile-web-app-title" content="Bybit Trading Bot">
    <link rel="manifest" href="/manifest.json">
    <link rel="icon" type="image/png" sizes="192x192" href="/static/icon-192x192.png">
    <link rel="apple-touch-icon" href="/static/icon-192x192.png">
    <title>Bybit Trading Bot - Mobile Control</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
            background: linear-gradient(135deg, #0f0f23, #1a1a2e, #16213e);
            color: #ffffff;
            overflow-x: hidden;
            user-select: none;
            -webkit-user-select: none;
            -webkit-touch-callout: none;
        }

        .app-container {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            position: relative;
        }

        .header {
            background: linear-gradient(45deg, #00ff88, #00ccff);
            padding: 20px;
            text-align: center;
            box-shadow: 0 4px 20px rgba(0, 255, 136, 0.3);
            position: sticky;
            top: 0;
            z-index: 100;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 15px;
        }

        .app-logo {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            padding: 5px;
            backdrop-filter: blur(10px);
            border: 2px solid rgba(0, 0, 0, 0.2);
        }

        .header-content {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .header h1 {
            font-size: 24px;
            font-weight: 800;
            color: #000;
            text-shadow: none;
            margin: 0;
        }

        .header .status {
            font-size: 14px;
            color: rgba(0, 0, 0, 0.8);
            font-weight: 600;
        }

        .main-content {
            flex: 1;
            padding: 20px;
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .control-panel {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 25px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }

        .control-title {
            font-size: 20px;
            font-weight: 700;
            margin-bottom: 20px;
            text-align: center;
            color: #00ff88;
        }

        .control-buttons {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
        }

        .control-btn {
            padding: 20px;
            border: none;
            border-radius: 15px;
            font-size: 16px;
            font-weight: 700;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
            position: relative;
            overflow: hidden;
        }

        .control-btn:before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .control-btn:active:before {
            left: 100%;
        }

        .start-btn {
            background: linear-gradient(45deg, #00ff88, #00cc77);
            color: #000;
            box-shadow: 0 4px 15px rgba(0, 255, 136, 0.4);
        }

        .start-btn:active {
            transform: scale(0.95);
            box-shadow: 0 2px 10px rgba(0, 255, 136, 0.6);
        }

        .stop-btn {
            background: linear-gradient(45deg, #ff4444, #cc3333);
            color: #fff;
            box-shadow: 0 4px 15px rgba(255, 68, 68, 0.4);
        }

        .stop-btn:active {
            transform: scale(0.95);
            box-shadow: 0 2px 10px rgba(255, 68, 68, 0.6);
        }

        .restart-btn {
            background: linear-gradient(45deg, #ffaa00, #ff8800);
            color: #000;
            box-shadow: 0 4px 15px rgba(255, 170, 0, 0.4);
            grid-column: 1 / -1;
        }

        .restart-btn:active {
            transform: scale(0.95);
            box-shadow: 0 2px 10px rgba(255, 170, 0, 0.6);
        }

        .emergency-btn {
            background: linear-gradient(45deg, #ff0066, #cc0044);
            color: #fff;
            grid-column: 1 / -1;
            margin-top: 10px;
            box-shadow: 0 4px 15px rgba(255, 0, 102, 0.4);
        }

        .emergency-btn:active {
            transform: scale(0.95);
            box-shadow: 0 2px 10px rgba(255, 0, 102, 0.6);
        }

        .dashboard {
            display: grid;
            gap: 15px;
        }

        .dashboard-card {
            background: rgba(255, 255, 255, 0.08);
            border-radius: 15px;
            padding: 20px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .dashboard-card h3 {
            color: #00ff88;
            font-size: 16px;
            margin-bottom: 10px;
            font-weight: 600;
        }

        .metric {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .metric-value {
            font-weight: 700;
            color: #00ccff;
        }

        .profit-positive {
            color: #00ff88;
        }

        .profit-negative {
            color: #ff4444;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-active {
            background: #00ff88;
            box-shadow: 0 0 10px rgba(0, 255, 136, 0.5);
        }

        .status-inactive {
            background: #ff4444;
            box-shadow: 0 0 10px rgba(255, 68, 68, 0.5);
        }

        .notification {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 255, 136, 0.9);
            color: #000;
            padding: 15px 25px;
            border-radius: 25px;
            font-weight: 600;
            box-shadow: 0 4px 20px rgba(0, 255, 136, 0.3);
            z-index: 1000;
            opacity: 0;
            transition: all 0.3s ease;
        }

        .notification.show {
            opacity: 1;
            transform: translateX(-50%) translateY(10px);
        }

        .notification.error {
            background: rgba(255, 68, 68, 0.9);
            color: #fff;
            box-shadow: 0 4px 20px rgba(255, 68, 68, 0.3);
        }

        .loading {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            justify-content: center;
            align-items: center;
            z-index: 2000;
        }

        .loading-spinner {
            width: 50px;
            height: 50px;
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-top: 4px solid #00ff88;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .quick-stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-bottom: 20px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 12px;
            padding: 15px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .stat-value {
            font-size: 24px;
            font-weight: 800;
            color: #00ff88;
            margin-bottom: 5px;
        }

        .stat-label {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.7);
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .refresh-btn {
            position: fixed;
            bottom: 20px;
            right: 20px;
            width: 60px;
            height: 60px;
            border: none;
            border-radius: 50%;
            background: linear-gradient(45deg, #00ff88, #00ccff);
            color: #000;
            font-size: 18px;
            cursor: pointer;
            box-shadow: 0 4px 20px rgba(0, 255, 136, 0.4);
            transition: all 0.3s ease;
        }

        .refresh-btn:active {
            transform: scale(0.9);
        }

        @media (max-width: 480px) {
            .main-content {
                padding: 15px;
                gap: 15px;
            }
            
            .control-panel {
                padding: 20px;
            }
            
            .control-btn {
                padding: 18px;
                font-size: 14px;
            }
        }
    </style>
</head>
<body>
    <div class="app-container">
        <div class="header">
            <div class="header-content">
                <img src="/static/logo.svg" alt="Trading Bot Logo" class="app-logo">
                <div>
                    <h1>Bybit Trading Bot</h1>
                    <div class="status" id="system-status">
                        <span class="status-indicator status-active"></span>
                        System Ready
                    </div>
                </div>
            </div>
        </div>

        <div class="main-content">
            <!-- Quick Stats -->
            <div class="quick-stats">
                <div class="stat-card">
                    <div class="stat-value" id="total-profit">$15,420</div>
                    <div class="stat-label">Total Profit</div>
                </div>
                <div class="stat-card">
                    <div class="stat-value" id="active-positions">8</div>
                    <div class="stat-label">Active Positions</div>
                </div>
            </div>

            <!-- System Controls -->
            <div class="control-panel">
                <div class="control-title">System Control</div>
                <div class="control-buttons">
                    <button class="control-btn start-btn" onclick="startSystem()">
                        🚀 START
                    </button>
                    <button class="control-btn stop-btn" onclick="stopSystem()">
                        ⏹️ STOP
                    </button>
                    <button class="control-btn restart-btn" onclick="restartSystem()">
                        🔄 RESTART SYSTEM
                    </button>
                    <button class="control-btn emergency-btn" onclick="emergencyStop()">
                        🚨 EMERGENCY STOP
                    </button>
                </div>
            </div>

            <!-- Dashboard -->
            <div class="dashboard">
                <div class="dashboard-card">
                    <h3>Trading Status</h3>
                    <div class="metric">
                        <span>API Connection:</span>
                        <span class="metric-value" id="api-status">Connected</span>
                    </div>
                    <div class="metric">
                        <span>Strategies Running:</span>
                        <span class="metric-value" id="strategies-count">12</span>
                    </div>
                    <div class="metric">
                        <span>Trading Mode:</span>
                        <span class="metric-value" id="trading-mode">LIVE</span>
                    </div>
                </div>

                <div class="dashboard-card">
                    <h3>Performance</h3>
                    <div class="metric">
                        <span>Today's P&L:</span>
                        <span class="metric-value profit-positive" id="daily-pnl">+$2,340</span>
                    </div>
                    <div class="metric">
                        <span>Success Rate:</span>
                        <span class="metric-value" id="success-rate">78.5%</span>
                    </div>
                    <div class="metric">
                        <span>Total Trades:</span>
                        <span class="metric-value" id="total-trades">156</span>
                    </div>
                </div>

                <div class="dashboard-card">
                    <h3>System Health</h3>
                    <div class="metric">
                        <span>CPU Usage:</span>
                        <span class="metric-value" id="cpu-usage">45.2%</span>
                    </div>
                    <div class="metric">
                        <span>Memory Usage:</span>
                        <span class="metric-value" id="memory-usage">62.8%</span>
                    </div>
                    <div class="metric">
                        <span>Uptime:</span>
                        <span class="metric-value" id="uptime">4h 32m</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Refresh Button -->
        <button class="refresh-btn" onclick="refreshData()">🔄</button>

        <!-- Loading Overlay -->
        <div class="loading" id="loading">
            <div class="loading-spinner"></div>
        </div>

        <!-- Notification -->
        <div class="notification" id="notification"></div>
    </div>

    <script>
        // Base URL for API calls
        const API_BASE = window.location.origin;
        
        // Global state
        let refreshInterval;
        let isSystemRunning = true;

        // Initialize app
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Mobile Control App Initialized');
            refreshData();
            startAutoRefresh();
        });

        // Show notification
        function showNotification(message, isError = false) {
            const notification = document.getElementById('notification');
            notification.textContent = message;
            notification.className = `notification ${isError ? 'error' : ''} show`;
            
            setTimeout(() => {
                notification.classList.remove('show');
            }, 3000);
        }

        // Show/hide loading
        function showLoading(show = true) {
            document.getElementById('loading').style.display = show ? 'flex' : 'none';
        }

        // Haptic feedback
        function hapticFeedback() {
            if (navigator.vibrate) {
                navigator.vibrate(50);
            }
        }

        // API call wrapper
        async function apiCall(endpoint, method = 'GET', data = null) {
            try {
                showLoading(true);
                
                const options = {
                    method,
                    headers: {
                        'Content-Type': 'application/json',
                    }
                };

                if (data && method !== 'GET') {
                    options.body = JSON.stringify(data);
                }

                const response = await fetch(`${API_BASE}${endpoint}`, options);
                const result = await response.json();
                
                showLoading(false);
                return result;
            } catch (error) {
                showLoading(false);
                console.error('API call failed:', error);
                showNotification('Connection failed. Check server status.', true);
                return null;
            }
        }

        // System control functions
        async function startSystem() {
            hapticFeedback();
            console.log('Starting trading system...');
            
            const result = await apiCall('/system/start', 'POST');
            if (result && result.status === 'success') {
                showNotification('Trading system started successfully!');
                isSystemRunning = true;
                refreshData();
            } else {
                showNotification(result?.message || 'Failed to start system', true);
            }
        }

        async function stopSystem() {
            hapticFeedback();
            console.log('Stopping trading system...');
            
            const result = await apiCall('/system/stop', 'POST');
            if (result && result.status === 'success') {
                showNotification('Trading system stopped successfully!');
                isSystemRunning = false;
                refreshData();
            } else {
                showNotification(result?.message || 'Failed to stop system', true);
            }
        }

        async function restartSystem() {
            hapticFeedback();
            console.log('Restarting trading system...');
            
            const result = await apiCall('/system/restart', 'POST');
            if (result && result.status === 'success') {
                showNotification('Trading system restarted successfully!');
                isSystemRunning = true;
                refreshData();
            } else {
                showNotification(result?.message || 'Failed to restart system', true);
            }
        }

        async function emergencyStop() {
            hapticFeedback();
            
            if (confirm('Are you sure you want to execute emergency stop? This will halt all trading immediately.')) {
                console.log('Emergency stop activated...');
                
                const result = await apiCall('/api/trading/emergency-stop', 'POST');
                if (result && result.status === 'success') {
                    showNotification('EMERGENCY STOP EXECUTED - All trading halted!');
                    isSystemRunning = false;
                    refreshData();
                } else {
                    showNotification(result?.message || 'Emergency stop failed', true);
                }
            }
        }

        // Data refresh functions
        async function refreshData() {
            console.log('Refreshing data...');
            
            // Get system status
            const status = await apiCall('/status');
            if (status) {
                updateSystemStatus(status);
            }

            // Get dashboard data
            const dashboard = await apiCall('/api/dashboard');
            if (dashboard) {
                updateDashboard(dashboard);
            }
        }

        function updateSystemStatus(status) {
            const statusElement = document.getElementById('system-status');
            const indicator = statusElement.querySelector('.status-indicator');
            
            if (status.trading_active) {
                statusElement.innerHTML = '<span class="status-indicator status-active"></span>System Running';
                isSystemRunning = true;
            } else {
                statusElement.innerHTML = '<span class="status-indicator status-inactive"></span>System Stopped';
                isSystemRunning = false;
            }

            // Update other status fields
            document.getElementById('api-status').textContent = status.api_connected ? 'Connected' : 'Disconnected';
            document.getElementById('strategies-count').textContent = status.strategies_running || 0;
            document.getElementById('trading-mode').textContent = status.mode || 'UNKNOWN';
        }

        function updateDashboard(data) {
            if (data.profit_loss) {
                document.getElementById('total-profit').textContent = `$${data.profit_loss.total.toLocaleString()}`;
                
                const dailyPnl = document.getElementById('daily-pnl');
                const dailyValue = data.profit_loss.today;
                dailyPnl.textContent = `${dailyValue >= 0 ? '+' : ''}$${dailyValue.toLocaleString()}`;
                dailyPnl.className = `metric-value ${dailyValue >= 0 ? 'profit-positive' : 'profit-negative'}`;
            }

            if (data.positions) {
                document.getElementById('active-positions').textContent = data.positions.open || 0;
                document.getElementById('total-trades').textContent = data.positions.closed_today || 0;
            }

            if (data.system_health) {
                document.getElementById('cpu-usage').textContent = `${data.system_health.cpu_usage}%`;
                document.getElementById('memory-usage').textContent = `${data.system_health.memory_usage}%`;
            }

            // Update success rate (mock for now)
            document.getElementById('success-rate').textContent = '78.5%';
            document.getElementById('uptime').textContent = '4h 32m';
        }

        // Auto-refresh setup
        function startAutoRefresh() {
            refreshInterval = setInterval(refreshData, 5000); // Refresh every 5 seconds
        }

        function stopAutoRefresh() {
            if (refreshInterval) {
                clearInterval(refreshInterval);
            }
        }

        // Handle app visibility change
        document.addEventListener('visibilitychange', function() {
            if (document.hidden) {
                stopAutoRefresh();
            } else {
                startAutoRefresh();
                refreshData();
            }
        });

        // Handle network status
        window.addEventListener('online', function() {
            showNotification('Connection restored');
            refreshData();
        });

        window.addEventListener('offline', function() {
            showNotification('Connection lost', true);
        });

        // Register service worker for PWA
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', function() {
                navigator.serviceWorker.register('/service-worker.js')
                    .then(function(registration) {
                        console.log('ServiceWorker registration successful');
                    })
                    .catch(function(err) {
                        console.log('ServiceWorker registration failed: ', err);
                    });
            });
        }
    </script>
</body>
</html>
