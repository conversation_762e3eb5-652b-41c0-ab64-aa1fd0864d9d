{"_id": "@types/scheduler", "_rev": "482-fcb8c0cda0abf9d3fb663eed24ba6ce7", "name": "@types/scheduler", "dist-tags": {"ts2.1": "0.12.0", "ts2.2": "0.12.0", "ts2.3": "0.12.0", "ts2.4": "0.12.0", "ts2.5": "0.12.0", "ts2.6": "0.12.0", "ts2.7": "0.12.0", "ts2.8": "0.16.1", "ts2.9": "0.16.1", "ts3.0": "0.16.1", "ts3.1": "0.16.1", "ts3.2": "0.16.1", "ts3.3": "0.16.1", "ts3.4": "0.16.1", "ts3.5": "0.16.1", "ts3.6": "0.16.2", "ts3.7": "0.16.2", "ts3.8": "0.16.2", "ts3.9": "0.16.2", "ts4.0": "0.16.2", "ts4.1": "0.16.2", "ts4.2": "0.16.2", "ts4.3": "0.16.3", "ts4.4": "0.16.3", "ts4.5": "0.16.8", "ts4.6": "0.16.8", "ts5.9": "0.26.0", "ts4.7": "0.23.0", "ts4.8": "0.23.0", "ts4.9": "0.23.0", "ts5.0": "0.23.0", "ts5.8": "0.26.0", "ts5.7": "0.26.0", "ts5.6": "0.26.0", "ts5.4": "0.26.0", "ts5.5": "0.26.0", "latest": "0.26.0", "ts5.1": "0.26.0", "ts5.2": "0.26.0", "ts5.3": "0.26.0"}, "versions": {"0.10.0": {"name": "@types/scheduler", "version": "0.10.0", "license": "MIT", "_id": "@types/scheduler@0.10.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/Methuselah96", "name": "<PERSON>", "githubUsername": "Methuselah96"}], "dist": {"shasum": "9b5617d5ac31993c460b69900714cfef788e5db4", "tarball": "https://registry.npmjs.org/@types/scheduler/-/scheduler-0.10.0.tgz", "fileCount": 4, "integrity": "sha512-kYsBuZdFKybL/G7PbB+ydX+4K13AN+HNGsXeF4SX1kduNbTWM8WqQaYE1xzbwEfNeyjzZ0azRmbpOKBHSl2pwA==", "signatures": [{"sig": "MEQCHz0DxVdYC46ncxACwW6mV1gJ9plr03/UkpG5YrMY5OwCIQDygHC8W2l7PgXcfuDHFT393ZLnVdfjxUPZjXWOBZEKPw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3581, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJb3+FpCRA9TVsSAnZWagAALqkP/0eVw/C7kqj5Bej2OJ5t\n9z1IYXkxLkXlbQXubm6u8CxkRTCWPu2/iaQZ6QO6JhkzsBAsfMpO1vgOXNff\n877+UGi329OWr+synStxASbY8HS9FV4reIAIIod7C6jpbvbfNHq9erI6EmNv\nsfZoZP/2Baw7sLoxpCe5Pw74W0bD9yTpL2wHxvHd01VGSxwQYE505VIxskDT\n0LZGucTBEXaCEANXkpMnB43tZAlzkScyzLLplUH09WLEheBC3wpbIUzbU4Q6\ntJGFPbpHdiZqPpmdGdr8KbHMejCxhPs2U7sBMrmMIkubigCogJwrlE1lAkc4\nh9qqbUcio7R24uelyUODzPltxdLJM/biOHV5GU/wJLHbs8b0rCuJrdX1v15n\nG/KhnApnjyvZh7r8UEflsEDC09L/ZzBku3uT3DITTg6Oy8hS1m+MKDz7W14C\nlePWKJb12MLfWsW/iDla8OYMyqV/iRWBuhKpP+1DDEQSR1vX+MMCBIqCk8VS\nQ9Ud0EumIeQQTxkYovFcKRa4+wIlaEN48gezcfRuzhRYLrkSDA4YVAtUGXbP\n9P2QsBh4V3pL4Q3V674H7/vcWHci+nA3lyHKzpOjjKUaw9mLh5zhG6FaTKGs\nE4YzwNuoTH/XGlk3ap80vcdq8cHwXwPtMBylt3Nm0dgrky7W4bo25NoBVVTX\nXXFj\r\n=V4LH\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for scheduler", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "2.1", "_npmOperationalInternal": {"tmp": "tmp/scheduler_0.10.0_1541398888276_0.18850927695785624", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "d4281c8c1bd9933ea6a15d652a403b1634b421e909716ccb1975099cec306109"}, "0.12.0": {"name": "@types/scheduler", "version": "0.12.0", "license": "MIT", "_id": "@types/scheduler@0.12.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/Methuselah96", "name": "<PERSON>", "githubUsername": "Methuselah96"}], "dist": {"shasum": "f2d6663802c4c8b4468dde655d0af610287b151c", "tarball": "https://registry.npmjs.org/@types/scheduler/-/scheduler-0.12.0.tgz", "fileCount": 4, "integrity": "sha512-tcmRUJMzhcwYS/AXg/dtj5/Rrs3Sf8IP2B1vpy8ZhYIBrDOrjpu9q6ysecD2rAqAidzmhzC8SZ8QLCtlFzjSrA==", "signatures": [{"sig": "MEYCIQCIDNFctlZy10RoziwgiptndkAf9DbaGe2MHLOrzyWhiAIhAO+Z17Kt7HkAifQ9n0XW0kNcIokJyC79pgm1osG64Thu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 3747, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcQSAJCRA9TVsSAnZWagAAZvAP/jmfF6V3IVwg3Bvfhv68\nKOc6Uhcn88gOYa1rOT8uMw6MWWTo98NyGjOasqoY2fRmJIAWGMpYJ2UGmjH5\nWSIaIdgC59T7Ef99d8QzWzgDijm3LOPiE/SNXIEoB1oFJmQG4k7UfqeYezpp\nrZn2jmcRxjmPMhFW9I1qr0G89rXhxcYjVPS6FoFxJ5NMpdlrtX5tDlGQZ0LR\n/QD3lvRih832CS0Q/bvWOFLkS2TholsQ92lgBRIztrq2axjTG0C/eZYgha2i\nLJJLKzdrgjbxXVaEXCdY3rrA9NK1lvp42OLS85h+UvD9FiMjCf5N50dmLLpl\nK5Wi3/UNCSWHI/1fb95w0mvQ4SJwF2pvB+21TaP1Qs9sPaq5Nm9tXzHlqYd4\nEmCXkVlinA8M5RyTbyZdVGAjE83LNXU90JqX4i2SsQjYgeXBTU9MrqufTu9A\nOuKHPVl4/vmhP0RCvaWrC/e2UvvfIDXWOyyAh0y+TNISEEbB22c7tK0ltcf5\n5wCbGtKEiyYIJZ0DNeWUTxyxQU//Zc5v/deIiFdoQQGiIsuxcISUGsPGYvBW\nclBKWwXf+MImFD2yqXchiWT0uIFjkz+3Jd2d56RocmtFQ5Yb2FyihTuAS9vI\n7gVMhZoOKd304Iwe+nus3bnHUKkDm7v48Dv6a54U7+o9t4hLrVUAdeJUN8Fa\nTnpQ\r\n=+CHL\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for scheduler", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "2.1", "_npmOperationalInternal": {"tmp": "tmp/scheduler_0.12.0_1547771913009_0.3560664649564984", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "680b4cf0ac8677e33e3a79157bc92e59a8f5a63fd92226112bd9f218871e4c0f"}, "0.12.1": {"name": "@types/scheduler", "version": "0.12.1", "license": "MIT", "_id": "@types/scheduler@0.12.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/Methuselah96", "name": "<PERSON>", "githubUsername": "Methuselah96"}, {"url": "https://github.com/eps1lon", "name": "<PERSON>", "githubUsername": "eps1lon"}], "dist": {"shasum": "3204ac7ac5c639b3d152a8a5e1ed5f3206d3c7b0", "tarball": "https://registry.npmjs.org/@types/scheduler/-/scheduler-0.12.1.tgz", "fileCount": 5, "integrity": "sha512-XyL0jqjNrtC5evlPzAx25XCUxyrP5PFgsWwbCNkj6CPlABEd9ikr1B3wGd9KiCad7wbLd7MpL3KKe2dGpLKnZw==", "signatures": [{"sig": "MEYCIQCEXWj7ZVfrl5twpEJi0MYrv04YvvFcOP/4mGMUu6gf7QIhANK2JYoPpoS2dYGLQkexonHy+aj1HDTvnYb2/Bj1hmr9", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8185, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJcrN4vCRA9TVsSAnZWagAAAbsQAJFhvCCn/3qdWTkFMIND\nu7gHkX3hK6AeBkjTgjClnf9aeOygGkDybd3btD2KGrB5pFG1XC6i7m4frtTJ\nJl89x2kB0pnbY1bfAaL+5ulzDzBCFAJHsNmOmiNww1f3W3PpKTMHihjeRcRe\nUESKjOFHwX/yuitfQNHC52HexQIszvz3kT1r4u0UWOIqcweOzHIQ1kJEmXQX\nXXLC7uscYPRuVaLGgYgKTzIL19XhZBNwhp55SnXkifbcDUvZszkufe3DMh7W\n17B158iutuCZXoDd99D85XUqwqZLjb1+QCGzYCqXqxQdmNjXaT5FpUar6cJE\nSyksTU1UcHbplofbkp1Y6zHe4c1ETOU2U0L3pbrWdwmy8yhIKquqJyh3oRK4\n/8PONghDyAgZJBYN9PL50q8oJEJt3eWR7m0lmBAuXA/XEcKGlVkq+hYFKh9m\nRyFtG1uiD3dsLeua/uSfCpjd60COP79ezKlQh9dVXx2wUZ2o95Qwafg3xslT\nokRpHihWK1o6GuRZL/IeyWGBo6HafIgIa2u5ZaWyDQcttrOyfMj+9aimS5lJ\no9Ce5phzXzjAoTz3LAJMM2IwmrrcY+P0/fePBJ9RcZJs6oD/oiW2E8Y/TWJ5\neIzAR9pPQcx6wOnL4UTf4d3vxEZE6u0EHmM0DQFjwb8V2fYneA0DuOCJSxAS\nA8jW\r\n=g58T\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/scheduler"}, "description": "TypeScript definitions for scheduler", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "2.8", "_npmOperationalInternal": {"tmp": "tmp/scheduler_0.12.1_1554832943162_0.8619528403675984", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "97d9b730fb6c67c488fe1bd0bae243c3750a411457bd240d4c01d39d251b8b6d"}, "0.16.0": {"name": "@types/scheduler", "version": "0.16.0", "license": "MIT", "_id": "@types/scheduler@0.16.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/Methuselah96", "name": "<PERSON>", "githubUsername": "Methuselah96"}, {"url": "https://github.com/eps1lon", "name": "<PERSON>", "githubUsername": "eps1lon"}], "dist": {"shasum": "0375e7f8ff3c75d0dc1c6cef5bfabc2a514f9ca4", "tarball": "https://registry.npmjs.org/@types/scheduler/-/scheduler-0.16.0.tgz", "fileCount": 5, "integrity": "sha512-Ru6zqw+j3Rhn6Dgg7j/SSKOC+2Rpm92V6f+tvXMZTZ8KgAqtOdD2fHWUYvcU2zrsciw46oTIWjeJ86Hun9AuZg==", "signatures": [{"sig": "MEQCIEa4ZaJBK1Ib7k2873iyZhnCHxywUh3OANaqXgp7eapnAiBVUavlVr+fjoGBJ87G5/V+vRRbpLmvvCKP2HhkG5LH/Q==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8216, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdllx9CRA9TVsSAnZWagAAOSkP/RV3nHzUeiTmL/tY/TiX\nX3zPuGbDF5M/IabquvLXKRrhxq8HKGCPefyl7uKHuc7nZewxqWbWlRzDiupN\n1FYVpOIr61s0N8Jbj6/PFjASQkgS1tZ2o3fTIEvxeCHJsNThNk71iOazXfib\nUC8/ALNpifmMQY9ze9FUOVxa1aUTmWH2/lX4egw1H60CK0Y3Dmw/V++ocBVE\nsZSOHsO5CFCQcmK3erfoJyBanLER9isUDxeVDmnGOAAzfljFnmp3zb3l86uF\nNlEZGK7j84KcydFGGoi5NIxqPNtAH/z/2GheKm2r9qR91eAtufjNXWgmMAJ0\n6XWFEwB+6uT11fGg8PijquAPbg79Q0pdoUcUwzTg2n37DMiWSo6de8orFKEX\ncLc/o9awjsJ6RJ8r/5WLpkBj6eV2ytTICVCXd3G3XVhU6yy2rZy1bQ0F9eeG\nRWQO6V6hdNDhOF9GMQ2zMLIokKklU4EvGZU7f60GuwTxva6BR6z1ye7iPZ3T\nUmJ+AuLRrriGyj1yv088HMS+ouTv0C+o4zDQJDmmLn1W+SQwD9YgwYMXD6Qu\nuxI5cyf3cJ8OcUA/IxRqqTtPvLVrToZL0ZfCQnknEJLrythdxAHVzWoZRSzg\n1lcmSsjGONztWKGTVgDTXhmO1Rbb3s4bda9/G4kglIqP/9+a8usafnOsq87O\n0tE6\r\n=f0D8\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/scheduler"}, "description": "TypeScript definitions for scheduler", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "2.8", "_npmOperationalInternal": {"tmp": "tmp/scheduler_0.16.0_1570135165253_0.5640897489174515", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "4129796eee54eabea79d46638599a96623646e59f20af7db392e6352cefe4926"}, "0.16.1": {"name": "@types/scheduler", "version": "0.16.1", "license": "MIT", "_id": "@types/scheduler@0.16.1", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/Methuselah96", "name": "<PERSON>", "githubUsername": "Methuselah96"}, {"url": "https://github.com/eps1lon", "name": "<PERSON>", "githubUsername": "eps1lon"}], "dist": {"shasum": "18845205e86ff0038517aab7a18a62a6b9f71275", "tarball": "https://registry.npmjs.org/@types/scheduler/-/scheduler-0.16.1.tgz", "fileCount": 5, "integrity": "sha512-EaCxbanVeyxDRTQBkdLb3Bvl/HK7PBK6UJjsSixB0iHKoWxE5uu2Q/DgtpOhPIojN0Zl1whvOd7PoHs2P0s5eA==", "signatures": [{"sig": "MEUCIQC83JzDR9bL2UeQXcAG6BxQm+bpzCaeK1kOIa2ehgFadQIgN638d8Yyuyth9hSIMER+/xF688SqWywp8+c+g1C1854=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8252, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJdphNoCRA9TVsSAnZWagAAieEP/0KD83PwTo8ZDnqGKKL+\nTxlwK52KXMN3vRkFouhoP73NmZpgwGJZw4QQfr3JQDkZ8MQIFkfu4WObrMwQ\nbzEfgXLr86eufnbs6q24Y3zjnbwCsnI/aCm/rLUoeUZ4c24qfPbZmILc7P2G\nAVuo1nTcY4jWjW4Pmib1yUB7HkR5hAeCeSNZ8GbY1+DIluX3g3NkNp1i8SWk\nlv884N7qptqoDm0apupNxfNOboTLTQHUBIzwNgHQ3PkXtNAck+UPgaP3fZ+A\nrT7KhOQ0hmyZFWpPNFUDAU53NumXVxbi5ap1iZUXi9SR3oAoIdm/V5TjNiCn\nxSeJHCF41LmpI+QcZVilqciJuU+AJ/6UuVzlgLH8lBxlghrjkHM4e0v2g0MY\nM31krdBxyNwgatn+6nRpN+yFOPM2OpFSKY6n3FDiz/gOoXijyRxqwmShZXA6\nRM8S9S8gpv7cfaXqAqhDLSf4uNlS0NimqwYZDYUvwwk3asRE5HVCEppV/TOg\nFOmmf9mqIuck5poblkdP468gVsupTlxUvH3GDmEZwh5TGjkZHxc3lQ/fk73/\nfFZoOAmNF7t0e4d5Uc1uycJ62oYM7XgqBDkGDY1nVpFqGQx9UP+outVXPTe6\ncWw5wo+VDd8HQAtaDjdo/CU+xHXo2N6CVr7a7GT1/1da4HiTEfTPqJxvXLwg\nLdc+\r\n=XCW4\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/scheduler"}, "description": "TypeScript definitions for scheduler", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "2.8", "_npmOperationalInternal": {"tmp": "tmp/scheduler_0.16.1_1571165031758_0.8998430705540541", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "de6e7ac9ebefa8aded2268fa4680d75483f921610b36328b5342711bb1f96af1"}, "0.16.2": {"name": "@types/scheduler", "version": "0.16.2", "license": "MIT", "_id": "@types/scheduler@0.16.2", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/Methuselah96", "name": "<PERSON>", "githubUsername": "Methuselah96"}, {"url": "https://github.com/eps1lon", "name": "<PERSON>", "githubUsername": "eps1lon"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/scheduler", "dist": {"shasum": "1a62f89525723dde24ba1b01b092bf5df8ad4d39", "tarball": "https://registry.npmjs.org/@types/scheduler/-/scheduler-0.16.2.tgz", "fileCount": 5, "integrity": "sha512-hppQEBDmlwhFAXKJX2KnWLYu5yMfi91yazPb2l+lbJiwW+wdo1gNeRA+3RgNSO39WYX2euey41KEwnqesU2Jew==", "signatures": [{"sig": "MEUCIQDRDm8/+YVHeyMLKUcClwHopHnLU0dPL+5jLRblObT/TQIgH3mjI0wlmMr8laeD6HIyginHGrOquS0QUYWWFGcghek=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8344, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg5IiBCRA9TVsSAnZWagAASU8P/00mEwMPQWPEqjVtTMLM\nBt369wlehMGhVoQ1fpKl7jw0FmUA3snas7XPjqEEyprYIokN4EnAA4vdyPFe\niT4e9Jekv40f4YxIHYBkeKXzFxAfOYJEC08TjpAE0qf4CHy1+Ou0kP/57dgc\ncCo0nXVbJDXaYbEyyrRynON2THYGzhHDQfGl7fjKcJWOgAF7BW7tKuVmSji+\n4CPENZKROVwctfTQS/FXghsxCgOk/oP5J+UhiCYELGcwAV37VXRl1PdiN6nF\nCWNSxctgqkPhR7SgruhWQEXK8IRHi8nvAWfqbsKOIqKyweSCJ8SWVf26TZqO\nFHroewd1ecdwuMQxlXiNOfr0eIsIPkmPwQJcCOf+qgmcSFqFRlCqFkYGnwwM\nG2qClcrRIoZZVCnJsekHGSy2FYgqhkXWOvpnaK1dZCtomqyXH6K+5XLbAM78\ni+ZA3o9iyKepXfWXCJ/70K9FqCR5sdhDtiyMSdhGv30h+nz6ho4Vhexp1wXh\n2kmzVMrsdlUk15ItEovN6YlZK1KdZa/mlLgvrX8LITAEs4oR7V4U+VO4hVEr\nEafYIDFhHBhj9umkemyRlRlf0gkYjd6/8Nq8c3Og3UGREeTEt5jjXQKgNKBn\n7caYf0LqRMSm8pVmc1nH4MgUnxjLXNILXs1X7ybJoRKpgQbzecz0QGdWEmxp\ncMA3\r\n=hWSw\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/scheduler"}, "description": "TypeScript definitions for scheduler", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "3.6", "_npmOperationalInternal": {"tmp": "tmp/scheduler_0.16.2_1625589889200_0.6085707496699111", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "122d740959245799b89613cc799b1a2e3274d1ee1db6c9abd7b6e4dadc0696ec"}, "0.16.3": {"name": "@types/scheduler", "version": "0.16.3", "license": "MIT", "_id": "@types/scheduler@0.16.3", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/Methuselah96", "name": "<PERSON>", "githubUsername": "Methuselah96"}, {"url": "https://github.com/eps1lon", "name": "<PERSON>", "githubUsername": "eps1lon"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/scheduler", "dist": {"shasum": "cef09e3ec9af1d63d2a6cc5b383a737e24e6dcf5", "tarball": "https://registry.npmjs.org/@types/scheduler/-/scheduler-0.16.3.tgz", "fileCount": 6, "integrity": "sha512-5cJ8CB4yAx7BH1oMvdU0Jh9lrEXyPkar6F9G/ERswkCuvP4KQZfZkSjcMbAICCpQTN4OuZn8tz0HiKv9TGZgrQ==", "signatures": [{"sig": "MEYCIQCsIkt+EBZ3U+1F322EJcA5DKysoWsgU6M2NdqLH52ksQIhAJZYkLlXYOTn1FB4kW+esA4wrAmrF5i8MByuoAEMIglN", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8363, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v4.10.10\r\nComment: https://openpgpjs.org\r\n\r\nwsFzBAEBCAAGBQJkG4kVACEJED1NWxICdlZqFiEECWMYAoorWMhJKdjhPU1b\r\nEgJ2VmrbFg/9EpOxbh939gDHt3jh54bHJuVGKHvlLRlKIfdc8zPRH3aIQDOT\r\nvTCSQHU/pfE8cQPu6mgizrg4S6r/rdDdZrj9VSqp0Ok/tcz3lmXZ+Jl7bgYI\r\niYamVtmhThbCG5oWdYPSCEkXuj2kFaPgJN3n+CizBv4Cd3WDphFLaNOjvCnn\r\n/YoFTxA3ycUcHNFEK2JonqLqvh5TUOT1pWE7Acwxp9Doem0GlgT00JbmQYyj\r\nxiooFMNb8nhHW18KrMfPRlvNNMvv2BokkLZtu1M6ISojEVXYwNskw8to146o\r\nal3sv57rdFAS5gTggXxTO/IC59pKmAMDTmTs74p2myfqoWV9wqvXioi6T/5r\r\nsiYMoXkDnY1j/XJPx1yjQRi1MtBVlEcLs+6XR2vrD2yikt4Cmk5Hd34wPyc/\r\n/ZqI5av9b4EVdMbhdMSqkZinW9pMAv/UUbb8mpS8QUQJ4HherPbwShNEF5zJ\r\nYqA1BhgAz25QFBLOudfwlHQ4eq+DwxGnMVQbPeePKeI9F2mrwgu2qxbi87mP\r\nRpF943oUioGwo+7oPc7lEcJwcTY/TcpRMSdeyzumFrqIwWsa+sHDLk+lPSRv\r\nmKrjC8tqGT4Y95YZxb/fFxbrmBFnfRHnRS0/ZgXlrVJHhKtIPoTpVC8xWQMa\r\nRcXa+F359ApYeXK7IqnzQdZA8RkCfWJxkfQ=\r\n=7X7+\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/scheduler"}, "description": "TypeScript definitions for scheduler", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "4.3", "_npmOperationalInternal": {"tmp": "tmp/scheduler_0.16.3_1679526165642_0.41029321512549277", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "33d8a6caa0110d038d21fa03caf7f899065858878f8ea9acaa7f755a8627732d"}, "0.16.4": {"name": "@types/scheduler", "version": "0.16.4", "license": "MIT", "_id": "@types/scheduler@0.16.4", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/Methuselah96", "name": "<PERSON>", "githubUsername": "Methuselah96"}, {"url": "https://github.com/eps1lon", "name": "<PERSON>", "githubUsername": "eps1lon"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/scheduler", "dist": {"shasum": "fedc3e5b15c26dc18faae96bf1317487cb3658cf", "tarball": "https://registry.npmjs.org/@types/scheduler/-/scheduler-0.16.4.tgz", "fileCount": 6, "integrity": "sha512-2L9ifAGl7wmXwP4v3pN4p2FLhD0O1qsJpvKmNin5VA8+UvNVb447UDaAEV6UdrkA+m/Xs58U1RFps44x6TFsVQ==", "signatures": [{"sig": "MEUCIQCU2NPFkUFqWfw5eSRtjMqnpavSoBZe77Yg02KVXch1TgIgYT34KBHHZMgS7ubF9EOSCpy4oJDgoLoO3UjdZ3G1pKk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8496}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/scheduler"}, "description": "TypeScript definitions for scheduler", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/scheduler_0.16.4_1695650065651_0.6025726434888521", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "95aa381db95b885d52bd28f4ef43293beed4cffd2b1732b8b24ffcf36d510c20"}, "0.16.5": {"name": "@types/scheduler", "version": "0.16.5", "license": "MIT", "_id": "@types/scheduler@0.16.5", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/Methuselah96", "name": "<PERSON>", "githubUsername": "Methuselah96"}, {"url": "https://github.com/eps1lon", "name": "<PERSON>", "githubUsername": "eps1lon"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/scheduler", "dist": {"shasum": "4751153abbf8d6199babb345a52e1eb4167d64af", "tarball": "https://registry.npmjs.org/@types/scheduler/-/scheduler-0.16.5.tgz", "fileCount": 6, "integrity": "sha512-s/FPdYRmZR8SjLWGMCuax7r3qCWQw9QKHzXVukAuuIJkXkDRwp+Pu5LMIVFi0Fxbav35WURicYr8u1QsoybnQw==", "signatures": [{"sig": "MEYCIQDpCfszr3XPcN/uo7nG4ZivpoBP3mLvjy442rBSw7JJewIhANy2bibLBMtQnmSew9NqcRvVMFx9D+uTzQJb2l+qORdL", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8168}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/scheduler"}, "description": "TypeScript definitions for scheduler", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/scheduler_0.16.5_1697654500790_0.37019869507481706", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "5b19acc73d214211fd5a0d88b2da060a4776b93433213d2cf3d825108fe02224"}, "0.16.6": {"name": "@types/scheduler", "version": "0.16.6", "license": "MIT", "_id": "@types/scheduler@0.16.6", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/Methuselah96", "name": "<PERSON>", "githubUsername": "Methuselah96"}, {"url": "https://github.com/eps1lon", "name": "<PERSON>", "githubUsername": "eps1lon"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/scheduler", "dist": {"shasum": "eb26db6780c513de59bee0b869ef289ad3068711", "tarball": "https://registry.npmjs.org/@types/scheduler/-/scheduler-0.16.6.tgz", "fileCount": 6, "integrity": "sha512-Vlktnchmkylvc9SnwwwozTv04L/e1NykF5vgoQ0XTmI8DD+wxfjQuHuvHS3p0r2jz2x2ghPs2h1FVeDirIteWA==", "signatures": [{"sig": "MEUCIQDzehAz9iDcgBSz8suKo9Dq4oDZSjyybRDrDDzOKsD5LAIgV8fRAy1ToNORnuoOU+WL2P6nQVa5NXup7cFQ0376qLw=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8168}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/scheduler"}, "description": "TypeScript definitions for scheduler", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/scheduler_0.16.6_1699372921232_0.12564175320854098", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "b01944d7cae04938d2883c03e4db09e00bc40d4c218c882141782a1e4ed4ab18"}, "0.16.7": {"name": "@types/scheduler", "version": "0.16.7", "license": "MIT", "_id": "@types/scheduler@0.16.7", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/Methuselah96", "name": "<PERSON>", "githubUsername": "Methuselah96"}, {"url": "https://github.com/eps1lon", "name": "<PERSON>", "githubUsername": "eps1lon"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/scheduler", "dist": {"shasum": "d62f1bd54724c84089f51f9218393930ba4abcf4", "tarball": "https://registry.npmjs.org/@types/scheduler/-/scheduler-0.16.7.tgz", "fileCount": 6, "integrity": "sha512-8g25Nl3AuB1KulTlSUsUhUo/oBgBU6XIXQ+XURpeioEbEJvkO7qI4vDfREv3vJYHHzqXjcAHvoJy4pTtSQNZtA==", "signatures": [{"sig": "MEQCIDFEmaf1TnYjcaDCK6qnWN3FJxAqgoRN5cr8yDsTvEiwAiAIg34lcZNIpUd5HB0FjxLhMVTDcuh0RtV+wy440SU+SA==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8186}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/scheduler"}, "description": "TypeScript definitions for scheduler", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/scheduler_0.16.7_1700528514056_0.2762842178400311", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "70d5c23755f986ab0cb85e37a2f6e77da065f7229dedf6963670f0ed2d174f49"}, "0.16.8": {"name": "@types/scheduler", "version": "0.16.8", "license": "MIT", "_id": "@types/scheduler@0.16.8", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/Methuselah96", "name": "<PERSON>", "githubUsername": "Methuselah96"}, {"url": "https://github.com/eps1lon", "name": "<PERSON>", "githubUsername": "eps1lon"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/scheduler", "dist": {"shasum": "ce5ace04cfeabe7ef87c0091e50752e36707deff", "tarball": "https://registry.npmjs.org/@types/scheduler/-/scheduler-0.16.8.tgz", "fileCount": 6, "integrity": "sha512-WZLiwShhwLRmeV6zH+GkbOFT6Z6VklCItrDioxUnv+u4Ll+8vKeFySoFyK/0ctcRpOmwAicELfmys1sDc/Rw+A==", "signatures": [{"sig": "MEUCIQCcHEyZYW3TYGvcnkJapcLIu7pQnwyrozHtZLLeqzKnPwIgTcM3IMJq2Pv9bitE84PpXH8oYbxJpkPbIf5KiYDNawY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 8254}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/scheduler"}, "description": "TypeScript definitions for scheduler", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/scheduler_0.16.8_1700614166297_0.9223778674249699", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "9d7d043580552b4f12920f8562a456a2a9799eee67136f013daf1bf22f17fbb6"}, "0.23.0": {"name": "@types/scheduler", "version": "0.23.0", "license": "MIT", "_id": "@types/scheduler@0.23.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/Methuselah96", "name": "<PERSON>", "githubUsername": "Methuselah96"}, {"url": "https://github.com/eps1lon", "name": "<PERSON>", "githubUsername": "eps1lon"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/scheduler", "dist": {"shasum": "0a6655b3e2708eaabca00b7372fafd7a792a7b09", "tarball": "https://registry.npmjs.org/@types/scheduler/-/scheduler-0.23.0.tgz", "fileCount": 5, "integrity": "sha512-YIoDCTH3Af6XM5VuwGG/QL/CJqga1Zm3NkU3HZ4ZHK2fRMPYP1VczsTUqtsf43PH/iJNVlPHAo2oWX7BSdB2Hw==", "signatures": [{"sig": "MEQCIHAUGek71cFlWT/Zd/VDy9C7BAdIV7EwQCAHwBtI/X1NAiAsn0Yt3rn7zmO5Qg3zpyIV+RH09xB6iw43kreaIi9+cQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5519}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/scheduler"}, "description": "TypeScript definitions for scheduler", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "typeScriptVersion": "4.7", "_npmOperationalInternal": {"tmp": "tmp/scheduler_0.23.0_1711440477741_0.989876092521556", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "d0aa79298fef5ad2c8802ed3691de2c1849dfcc22f231f196d2053ae10087bed"}, "0.26.0": {"name": "@types/scheduler", "version": "0.26.0", "license": "MIT", "_id": "@types/scheduler@0.26.0", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/Methuselah96", "name": "<PERSON>", "githubUsername": "Methuselah96"}, {"url": "https://github.com/eps1lon", "name": "<PERSON>", "githubUsername": "eps1lon"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/scheduler", "dist": {"shasum": "2b7183b9bbb622d130b23bedf06899b7fec7eed5", "tarball": "https://registry.npmjs.org/@types/scheduler/-/scheduler-0.26.0.tgz", "fileCount": 5, "integrity": "sha512-WFHp9YUJQ6CKshqoC37iOlHnQSmxNc795UhB26CyBBttrN9svdIrUjl/NjnNmfcwtncN0h/0PPAFWv9ovP8mLA==", "signatures": [{"sig": "MEUCIApwZZpcOGoh2H2CHXiTckHFfgs4zn7QtK19pH+pePVoAiEAv7cLixMt16q/O8crjcxWHhjtPw05pLMdwwt4u2085Nw=", "keyid": "SHA256:DhQ8wR5APBvFHLF/+Tc+AYvPOdTpcIDqOhxsBHRwC7U"}], "unpackedSize": 5545}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/scheduler"}, "description": "TypeScript definitions for scheduler", "directories": {}, "dependencies": {}, "_hasShrinkwrap": false, "peerDependencies": {}, "typeScriptVersion": "5.1", "_npmOperationalInternal": {"tmp": "tmp/scheduler_0.26.0_1743579188705_0.2475326562455762", "host": "s3://npm-registry-packages-npm-production"}, "typesPublisherContentHash": "da4a66c8598d6abe642c123415ff742736404b4acb294242859c381283dc67b3"}}, "time": {"created": "2018-11-05T06:21:28.056Z", "modified": "2025-04-02T07:33:15.254Z", "0.10.0": "2018-11-05T06:21:28.489Z", "0.12.0": "2019-01-18T00:38:33.118Z", "0.12.1": "2019-04-09T18:02:23.298Z", "0.16.0": "2019-10-03T20:39:25.419Z", "0.16.1": "2019-10-15T18:43:51.903Z", "0.16.2": "2021-07-06T16:44:49.372Z", "0.16.3": "2023-03-22T23:02:45.809Z", "0.16.4": "2023-09-25T13:54:25.842Z", "0.16.5": "2023-10-18T18:41:40.991Z", "0.16.6": "2023-11-07T16:02:01.487Z", "0.16.7": "2023-11-21T01:01:54.267Z", "0.16.8": "2023-11-22T00:49:26.459Z", "0.23.0": "2024-03-26T08:07:57.888Z", "0.26.0": "2025-04-02T07:33:08.893Z"}, "license": "MIT", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/scheduler", "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/scheduler"}, "description": "TypeScript definitions for scheduler", "contributors": [{"url": "https://github.com/Methuselah96", "name": "<PERSON>", "githubUsername": "Methuselah96"}, {"url": "https://github.com/eps1lon", "name": "<PERSON>", "githubUsername": "eps1lon"}], "maintainers": [{"name": "types", "email": "<EMAIL>"}], "readme": "[object Object]", "readmeFilename": ""}