<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Install Bybit Trading Bot on Your Phone</title>
    <meta name="description" content="Install the Bybit Trading Bot on your phone in seconds. Professional AI-powered trading system with real-time monitoring.">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
            color: #ffffff;
            line-height: 1.6;
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 40px 0;
        }

        .logo {
            font-size: 48px;
            margin-bottom: 16px;
        }

        .header h1 {
            font-size: 32px;
            font-weight: 700;
            margin-bottom: 12px;
            background: linear-gradient(135deg, #00ff88 0%, #00cc6a 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .header p {
            font-size: 18px;
            color: #a0a0a0;
            max-width: 600px;
            margin: 0 auto;
        }

        .installation-methods {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-bottom: 60px;
        }

        .method-card {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 20px;
            padding: 30px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .method-card:hover {
            transform: translateY(-5px);
            border-color: #00ff88;
            box-shadow: 0 20px 40px rgba(0, 255, 136, 0.1);
        }

        .method-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #00ff88, #00cc6a);
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .method-card:hover::before {
            opacity: 1;
        }

        .method-icon {
            font-size: 48px;
            margin-bottom: 20px;
            display: block;
        }

        .method-title {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 12px;
            color: #ffffff;
        }

        .method-subtitle {
            font-size: 16px;
            color: #00ff88;
            margin-bottom: 16px;
            font-weight: 500;
        }

        .method-description {
            color: #c0c0c0;
            margin-bottom: 24px;
            font-size: 15px;
        }

        .method-steps {
            list-style: none;
            margin-bottom: 24px;
        }

        .method-steps li {
            display: flex;
            align-items: flex-start;
            margin-bottom: 12px;
            font-size: 14px;
            color: #e0e0e0;
        }

        .step-number {
            background: #00ff88;
            color: #000;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 12px;
            margin-right: 12px;
            flex-shrink: 0;
            margin-top: 2px;
        }

        .install-button {
            background: linear-gradient(135deg, #00ff88 0%, #00cc6a 100%);
            color: #000;
            border: none;
            padding: 14px 28px;
            border-radius: 12px;
            font-weight: 600;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            margin-bottom: 12px;
        }

        .install-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 255, 136, 0.3);
        }

        .secondary-button {
            background: transparent;
            color: #00ff88;
            border: 1px solid #00ff88;
            padding: 12px 24px;
            border-radius: 12px;
            font-weight: 600;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
            text-decoration: none;
            display: inline-block;
            text-align: center;
        }

        .secondary-button:hover {
            background: rgba(0, 255, 136, 0.1);
        }

        .qr-code {
            text-align: center;
            margin: 40px 0;
            padding: 40px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .qr-placeholder {
            width: 200px;
            height: 200px;
            background: #ffffff;
            margin: 0 auto 20px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 48px;
            color: #000;
        }

        .device-detection {
            background: rgba(0, 255, 136, 0.1);
            border: 1px solid #00ff88;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 30px;
            text-align: center;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 40px 0;
        }

        .feature-item {
            display: flex;
            align-items: center;
            padding: 16px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 12px;
            font-size: 14px;
        }

        .feature-icon {
            font-size: 20px;
            margin-right: 12px;
        }

        .recommended-badge {
            background: #00ff88;
            color: #000;
            font-size: 12px;
            font-weight: 600;
            padding: 4px 8px;
            border-radius: 6px;
            position: absolute;
            top: 15px;
            right: 15px;
        }

        @media (max-width: 768px) {
            .installation-methods {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 28px;
            }
            
            .container {
                padding: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">📱💹</div>
            <h1>Install Trading Bot on Your Phone</h1>
            <p>Professional AI-powered trading system with real-time monitoring, autonomous strategies, and instant emergency controls.</p>
        </div>

        <div class="device-detection" id="device-info">
            <strong>📋 Device Detected:</strong> <span id="detected-device">Detecting...</span>
        </div>

        <div class="qr-code">
            <h3 style="margin-bottom: 20px; color: #00ff88;">📱 Scan with Your Phone</h3>
            <div class="qr-placeholder" id="qr-code">
                📊
            </div>
            <p style="color: #a0a0a0; margin-bottom: 16px;">
                Scan this QR code with your phone's camera
            </p>
            <div style="font-family: monospace; background: #000; padding: 12px; border-radius: 8px; color: #00ff88; word-break: break-all;">
                http://*************:8000/mobile-install
            </div>
        </div>

        <div class="installation-methods">
            <!-- PWA Installation (Recommended) -->
            <div class="method-card">
                <div class="recommended-badge">RECOMMENDED</div>
                <span class="method-icon">🚀</span>
                <h3 class="method-title">Progressive Web App</h3>
                <p class="method-subtitle">Instant Installation - No App Store</p>
                <p class="method-description">
                    Install directly from your browser. Works like a native app with offline support, push notifications, and home screen access.
                </p>
                <ol class="method-steps">
                    <li><span class="step-number">1</span>Open the link on your phone</li>
                    <li><span class="step-number">2</span>Tap the install prompt or "Add to Home Screen"</li>
                    <li><span class="step-number">3</span>Start trading from your home screen!</li>
                </ol>
                <button class="install-button" onclick="openPWA()">
                    Open Trading Bot PWA
                </button>
                <a href="#pwa-guide" class="secondary-button">View Installation Guide</a>
            </div>

            <!-- APK Installation -->
            <div class="method-card">
                <span class="method-icon">📦</span>
                <h3 class="method-title">Android APK</h3>
                <p class="method-subtitle">Native Android App</p>
                <p class="method-description">
                    Download and install the native Android app. Optimized for your Motorola G32 with full device integration.
                </p>
                <ol class="method-steps">
                    <li><span class="step-number">1</span>Download the APK file</li>
                    <li><span class="step-number">2</span>Enable "Install unknown apps"</li>
                    <li><span class="step-number">3</span>Install and open the app</li>
                </ol>
                <button class="install-button" onclick="downloadAPK()">
                    Download APK
                </button>
                <a href="#apk-guide" class="secondary-button">Build Instructions</a>
            </div>

            <!-- Web Access -->
            <div class="method-card">
                <span class="method-icon">🌐</span>
                <h3 class="method-title">Mobile Web App</h3>
                <p class="method-subtitle">Browser-Based Access</p>
                <p class="method-description">
                    Access the full trading interface through your mobile browser. No installation required, works on any device.
                </p>
                <ol class="method-steps">
                    <li><span class="step-number">1</span>Open browser on your phone</li>
                    <li><span class="step-number">2</span>Navigate to the trading bot URL</li>
                    <li><span class="step-number">3</span>Bookmark for quick access</li>
                </ol>
                <button class="install-button" onclick="openWebApp()">
                    Open Web Interface
                </button>
                <a href="http://*************:8000" class="secondary-button">Direct Link</a>
            </div>
        </div>

        <div class="features-grid">
            <div class="feature-item">
                <span class="feature-icon">📊</span>
                Real-time Dashboard
            </div>
            <div class="feature-item">
                <span class="feature-icon">🤖</span>
                AI Trading Strategies
            </div>
            <div class="feature-item">
                <span class="feature-icon">🔔</span>
                Push Notifications
            </div>
            <div class="feature-item">
                <span class="feature-icon">🛡️</span>
                Emergency Controls
            </div>
            <div class="feature-item">
                <span class="feature-icon">📱</span>
                Mobile Optimized
            </div>
            <div class="feature-item">
                <span class="feature-icon">🔒</span>
                Secure Authentication
            </div>
        </div>

        <!-- Installation Guides -->
        <div id="pwa-guide" style="margin-top: 60px; padding: 40px; background: rgba(255, 255, 255, 0.05); border-radius: 20px;">
            <h2 style="color: #00ff88; margin-bottom: 20px;">📱 PWA Installation Guide</h2>
            
            <h3 style="margin: 20px 0 10px 0; color: #ffffff;">Android Chrome:</h3>
            <ol style="color: #c0c0c0; margin-left: 20px;">
                <li>Open Chrome and visit <strong>http://*************:8000</strong></li>
                <li>Look for the "Install" banner at the bottom</li>
                <li>Tap "Install" or the menu (⋮) > "Add to Home screen"</li>
                <li>Confirm installation</li>
            </ol>

            <h3 style="margin: 20px 0 10px 0; color: #ffffff;">iPhone Safari:</h3>
            <ol style="color: #c0c0c0; margin-left: 20px;">
                <li>Open Safari and visit the URL</li>
                <li>Tap the Share button (□↗)</li>
                <li>Scroll down and tap "Add to Home Screen"</li>
                <li>Tap "Add" to confirm</li>
            </ol>
        </div>

        <div id="apk-guide" style="margin-top: 30px; padding: 40px; background: rgba(255, 255, 255, 0.05); border-radius: 20px;">
            <h2 style="color: #00ff88; margin-bottom: 20px;">📦 APK Installation Guide</h2>
            
            <h3 style="margin: 20px 0 10px 0; color: #ffffff;">Quick Build:</h3>
            <div style="background: #000; padding: 16px; border-radius: 8px; font-family: monospace; color: #00ff88; margin: 10px 0;">
cd mobile<br>
npm run build:moto-g32
            </div>
            
            <h3 style="margin: 20px 0 10px 0; color: #ffffff;">Manual Installation:</h3>
            <ol style="color: #c0c0c0; margin-left: 20px;">
                <li>Go to Settings > Security > Install unknown apps</li>
                <li>Enable for your file manager or browser</li>
                <li>Download the APK file</li>
                <li>Open and install the APK</li>
                <li>Grant necessary permissions</li>
            </ol>
        </div>
    </div>

    <script>
        // Device detection
        function detectDevice() {
            const userAgent = navigator.userAgent;
            const deviceElement = document.getElementById('detected-device');
            
            if (/Android/i.test(userAgent)) {
                deviceElement.textContent = 'Android Device - PWA Recommended';
                deviceElement.style.color = '#00ff88';
            } else if (/iPhone|iPad|iPod/i.test(userAgent)) {
                deviceElement.textContent = 'iOS Device - PWA Supported';
                deviceElement.style.color = '#00ff88';
            } else {
                deviceElement.textContent = 'Desktop/Other - Use QR Code';
                deviceElement.style.color = '#ffa500';
            }
        }

        // Generate QR code (simplified - in production use a QR library)
        function generateQRCode() {
            const qrElement = document.getElementById('qr-code');
            const url = 'http://*************:8000';
            
            // For production, use a QR code library like qrcode.js
            // For now, show a placeholder with instructions
            qrElement.innerHTML = `
                <div style="color: #666; font-size: 14px; padding: 20px;">
                    QR Code for:<br>
                    <strong style="color: #000;">${url}</strong><br><br>
                    📷 Use your phone's camera<br>
                    to scan this code
                </div>
            `;
        }

        // PWA installation
        function openPWA() {
            const url = 'http://*************:8000';
            if (/Android|iPhone|iPad|iPod/i.test(navigator.userAgent)) {
                window.location.href = url;
            } else {
                alert(`Open this URL on your phone:\n${url}`);
            }
        }

        // APK download
        function downloadAPK() {
            alert('APK build required. Run:\n\ncd mobile\nnpm run build:moto-g32');
        }

        // Web app access
        function openWebApp() {
            window.open('http://*************:8000', '_blank');
        }

        // Initialize
        detectDevice();
        generateQRCode();

        // Check if accessed from mobile
        if (/Android|iPhone|iPad|iPod/i.test(navigator.userAgent)) {
            document.querySelector('.device-detection').innerHTML = `
                <strong>🎉 Perfect!</strong> You're already on your phone. 
                <a href="http://*************:8000" style="color: #00ff88; text-decoration: none; font-weight: 600;">
                    → Open Trading Bot Now
                </a>
            `;
        }
    </script>
</body>
</html>
