# App Icons for Bybit Trading Bot PWA

This directory contains all the necessary icons for the Progressive Web App installation.

## Icon Sizes Needed:

- favicon.ico (16x16, 32x32, 48x48)
- icon-16x16.png
- icon-32x32.png  
- icon-72x72.png
- icon-96x96.png
- icon-128x128.png
- icon-144x144.png
- icon-152x152.png
- icon-192x192.png
- icon-384x384.png
- icon-512x512.png

## Generate Icons:

You can use any of these methods to generate the icons:

1. **Online Tools:**
   - https://favicon.io/favicon-generator/
   - https://realfavicongenerator.net/
   - https://www.favicon-generator.org/

2. **Create from Logo:**
   - Use your trading bot logo
   - Recommended colors: #00ff88 (green) on #0a0a0a (black) background
   - Include trading symbols: 📈 📊 💹

3. **Quick SVG Template:**
```svg
<svg width="512" height="512" xmlns="http://www.w3.org/2000/svg">
  <rect width="512" height="512" rx="90" fill="#0a0a0a"/>
  <circle cx="256" cy="200" r="60" fill="#00ff88"/>
  <rect x="196" y="280" width="120" height="8" rx="4" fill="#00ff88"/>
  <rect x="176" y="300" width="160" height="6" rx="3" fill="#00ff88"/>
  <rect x="156" y="320" width="200" height="4" rx="2" fill="#00ff88"/>
  <text x="256" y="380" text-anchor="middle" fill="#00ff88" font-family="Inter" font-size="48" font-weight="600">TB</text>
</svg>
```

## Placeholder Icons:
For now, the PWA will use fallback icons. Replace these with actual icons for better branding.
