import React, { useState, useEffect } from 'react';
import {
  Grid,
  Card,
  CardContent,
  Typography,
  Box,
  LinearProgress,
  Chip,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider
} from '@mui/material';
import {
  AccountBalance,
  TrendingUp,
  TrendingDown,
  Speed,
  Timer,
  ShowChart,
  MonetizationOn
} from '@mui/icons-material';
import TradingChart from './TradingChart';

const TradingDashboard = () => {
  const [stats, setStats] = useState({
    totalProfit: 0,
    todayProfit: 0,
    totalTrades: 0,
    successRate: 0,
    activePositions: 0,
    balance: 0
  });

  const [recentTrades, setRecentTrades] = useState([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Simulate loading trading data
    const loadTradingData = () => {
      // Simulate API call
      setTimeout(() => {
        setStats({
          totalProfit: 2847.32,
          todayProfit: 156.78,
          totalTrades: 1247,
          successRate: 73.2,
          activePositions: 3,
          balance: 15420.50
        });

        setRecentTrades([
          {
            id: 1,
            symbol: 'BTCUSDT',
            side: 'BUY',
            amount: 0.025,
            price: 45234.50,
            profit: 23.45,
            time: new Date(Date.now() - 300000).toISOString()
          },
          {
            id: 2,
            symbol: 'ETHUSDT',
            side: 'SELL',
            amount: 0.5,
            price: 2834.20,
            profit: -12.30,
            time: new Date(Date.now() - 600000).toISOString()
          },
          {
            id: 3,
            symbol: 'ADAUSDT',
            side: 'BUY',
            amount: 1000,
            price: 0.4567,
            profit: 45.67,
            time: new Date(Date.now() - 900000).toISOString()
          }
        ]);

        setIsLoading(false);
      }, 1000);
    };

    loadTradingData();
    const interval = setInterval(loadTradingData, 30000); // Update every 30 seconds

    return () => clearInterval(interval);
  }, []);

  const StatCard = ({ title, value, icon, color = 'primary', suffix = '' }) => (
    <Card>
      <CardContent>
        <Box display="flex" alignItems="center" justifyContent="space-between">
          <Box>
            <Typography color="text.secondary" gutterBottom variant="body2">
              {title}
            </Typography>
            <Typography variant="h5" component="div">
              {isLoading ? '...' : `${value}${suffix}`}
            </Typography>
          </Box>
          <Box color={`${color}.main`}>
            {icon}
          </Box>
        </Box>
      </CardContent>
    </Card>
  );

  return (
    <Grid container spacing={3}>
      {/* Stats Cards */}
      <Grid item xs={12} sm={6} md={4}>
        <StatCard
          title="Total Balance"
          value={stats.balance.toLocaleString()}
          icon={<AccountBalance />}
          color="primary"
          suffix=" USDT"
        />
      </Grid>
      
      <Grid item xs={12} sm={6} md={4}>
        <StatCard
          title="Total Profit"
          value={stats.totalProfit.toLocaleString()}
          icon={<MonetizationOn />}
          color="success"
          suffix=" USDT"
        />
      </Grid>
      
      <Grid item xs={12} sm={6} md={4}>
        <StatCard
          title="Today's Profit"
          value={stats.todayProfit.toLocaleString()}
          icon={stats.todayProfit >= 0 ? <TrendingUp /> : <TrendingDown />}
          color={stats.todayProfit >= 0 ? "success" : "error"}
          suffix=" USDT"
        />
      </Grid>

      <Grid item xs={12} sm={6} md={4}>
        <StatCard
          title="Total Trades"
          value={stats.totalTrades.toLocaleString()}
          icon={<ShowChart />}
          color="info"
        />
      </Grid>

      <Grid item xs={12} sm={6} md={4}>
        <StatCard
          title="Success Rate"
          value={stats.successRate}
          icon={<Speed />}
          color="warning"
          suffix="%"
        />
      </Grid>

      <Grid item xs={12} sm={6} md={4}>
        <StatCard
          title="Active Positions"
          value={stats.activePositions}
          icon={<Timer />}
          color="secondary"
        />
      </Grid>

      {/* Trading Chart */}
      <Grid item xs={12} lg={8}>
        <TradingChart symbol="BTCUSDT" timeframe="1m" />
      </Grid>

      {/* Recent Trades */}
      <Grid item xs={12} lg={4}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Recent Trades
            </Typography>
            {isLoading ? (
              <LinearProgress />
            ) : (
              <List dense>
                {recentTrades.map((trade, index) => (
                  <React.Fragment key={trade.id}>
                    <ListItem>
                      <ListItemIcon>
                        {trade.profit >= 0 ? (
                          <TrendingUp color="success" />
                        ) : (
                          <TrendingDown color="error" />
                        )}
                      </ListItemIcon>
                      <ListItemText
                        primary={
                          <Box display="flex" justifyContent="space-between" alignItems="center">
                            <Typography variant="body2">
                              {trade.symbol}
                            </Typography>
                            <Chip
                              label={trade.side}
                              size="small"
                              color={trade.side === 'BUY' ? 'success' : 'error'}
                              variant="outlined"
                            />
                          </Box>
                        }
                        secondary={
                          <Box>
                            <Typography variant="caption" display="block">
                              Amount: {trade.amount} @ ${trade.price}
                            </Typography>
                            <Typography
                              variant="caption"
                              color={trade.profit >= 0 ? 'success.main' : 'error.main'}
                            >
                              P&L: {trade.profit >= 0 ? '+' : ''}${trade.profit}
                            </Typography>
                          </Box>
                        }
                      />
                    </ListItem>
                    {index < recentTrades.length - 1 && <Divider />}
                  </React.Fragment>
                ))}
              </List>
            )}
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );
};

export default TradingDashboard;
