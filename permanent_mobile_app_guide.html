<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Permanent Mobile Trading App - Installation Guide</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
            background: linear-gradient(135deg, #0f0f23, #1a1a2e, #16213e);
            color: #ffffff;
            line-height: 1.6;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            background: linear-gradient(45deg, #00ff88, #00ccff);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .header h1 {
            font-size: 2.5rem;
            font-weight: 800;
            margin-bottom: 10px;
        }

        .subtitle {
            font-size: 1.2rem;
            color: #cccccc;
            margin-bottom: 30px;
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }

        .feature-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .feature-card h3 {
            color: #00ff88;
            font-size: 1.3rem;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
        }

        .feature-card .icon {
            font-size: 1.5rem;
            margin-right: 10px;
        }

        .installation-section {
            background: rgba(255, 255, 255, 0.08);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 30px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .installation-section h2 {
            color: #00ff88;
            font-size: 1.8rem;
            margin-bottom: 20px;
            text-align: center;
        }

        .install-methods {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .install-method {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .install-method h4 {
            color: #00ccff;
            margin-bottom: 15px;
        }

        .install-btn {
            display: inline-block;
            background: linear-gradient(45deg, #00ff88, #00ccff);
            color: #000;
            padding: 15px 25px;
            border-radius: 25px;
            text-decoration: none;
            font-weight: 700;
            margin: 10px;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 1px;
        }

        .install-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0, 255, 136, 0.3);
        }

        .install-btn.secondary {
            background: linear-gradient(45deg, #ffaa00, #ff8800);
        }

        .install-btn.danger {
            background: linear-gradient(45deg, #ff4444, #cc3333);
            color: #fff;
        }

        .steps {
            list-style: none;
            counter-reset: step-counter;
        }

        .steps li {
            counter-increment: step-counter;
            margin-bottom: 15px;
            padding-left: 40px;
            position: relative;
        }

        .steps li::before {
            content: counter(step-counter);
            position: absolute;
            left: 0;
            top: 0;
            background: #00ff88;
            color: #000;
            width: 25px;
            height: 25px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 700;
            font-size: 14px;
        }

        .important-note {
            background: linear-gradient(45deg, rgba(255, 170, 0, 0.2), rgba(255, 136, 0, 0.2));
            border-left: 4px solid #ffaa00;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }

        .important-note h4 {
            color: #ffaa00;
            margin-bottom: 10px;
        }

        .qr-section {
            text-align: center;
            margin: 30px 0;
        }

        .qr-code {
            background: #fff;
            padding: 20px;
            border-radius: 15px;
            display: inline-block;
            margin: 20px;
        }

        .system-requirements {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }

        @media (max-width: 768px) {
            .container {
                padding: 15px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .feature-grid,
            .install-methods {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Permanent Mobile Trading App</h1>
            <p class="subtitle">One-Click System Control + Independent Operation</p>
        </div>

        <div class="feature-grid">
            <div class="feature-card">
                <h3><span class="icon">🚀</span>One-Click Control</h3>
                <p>Start, stop, and restart the entire trading system with a single tap. Emergency stop available for instant halt.</p>
            </div>
            
            <div class="feature-card">
                <h3><span class="icon">⚡</span>Independent Operation</h3>
                <p>Trading system runs autonomously without the mobile app. App provides optional control interface.</p>
            </div>
            
            <div class="feature-card">
                <h3><span class="icon">📱</span>Permanent Installation</h3>
                <p>Install once, use forever. Works offline and syncs when connected. True native app experience.</p>
            </div>
            
            <div class="feature-card">
                <h3><span class="icon">📊</span>Real-Time Dashboard</h3>
                <p>Live profit/loss tracking, position monitoring, system health status, and performance metrics.</p>
            </div>
        </div>

        <div class="installation-section">
            <h2>🔧 Installation Methods</h2>
            
            <div class="install-methods">
                <div class="install-method">
                    <h4>📱 Progressive Web App</h4>
                    <p>Install directly to home screen</p>
                    <a href="http://*************:8000/mobile" class="install-btn">Install PWA</a>
                </div>
                
                <div class="install-method">
                    <h4>🎯 Direct Control</h4>
                    <p>Access control interface</p>
                    <a href="http://*************:8000/control" class="install-btn">Open Control</a>
                </div>
                
                <div class="install-method">
                    <h4>🌐 Web Access</h4>
                    <p>Browser-based access</p>
                    <a href="http://*************:8000" class="install-btn secondary">Web App</a>
                </div>
            </div>

            <div class="qr-section">
                <h3>📱 Scan QR Code for Quick Install</h3>
                <div class="qr-code">
                    <div id="qr-control" style="width: 200px; height: 200px;"></div>
                </div>
                <p>Scan with your phone's camera to install the permanent mobile app</p>
            </div>
        </div>

        <div class="installation-section">
            <h2>📋 Installation Steps</h2>
            
            <ol class="steps">
                <li><strong>Open your mobile browser</strong> (Chrome, Safari, Firefox, or DuckDuckGo)</li>
                <li><strong>Navigate to:</strong> http://*************:8000/mobile</li>
                <li><strong>Add to Home Screen:</strong>
                    <ul style="margin-top: 10px; margin-left: 20px;">
                        <li><strong>iPhone:</strong> Tap Share → Add to Home Screen</li>
                        <li><strong>Android:</strong> Tap Menu → Add to Home Screen</li>
                    </ul>
                </li>
                <li><strong>Launch the app</strong> from your home screen</li>
                <li><strong>Start controlling</strong> your trading system with one-click buttons</li>
            </ol>
        </div>

        <div class="important-note">
            <h4>🔥 IMPORTANT: Independent Operation Guarantee</h4>
            <p><strong>The trading system runs completely independently of this mobile app.</strong> The mobile app provides a control interface, but the trading system operates autonomously even when the app is closed. Your trading continues 24/7 regardless of mobile app usage.</p>
        </div>

        <div class="installation-section">
            <h2>🎮 Control Features</h2>
            
            <div class="feature-grid">
                <div class="feature-card">
                    <h3>🚀 START System</h3>
                    <p>Initialize and start the complete trading system with all strategies and AI components.</p>
                </div>
                
                <div class="feature-card">
                    <h3>⏹️ STOP System</h3>
                    <p>Gracefully stop all trading activities while preserving system state and data.</p>
                </div>
                
                <div class="feature-card">
                    <h3>🔄 RESTART System</h3>
                    <p>Complete system restart - stops all processes and reinitializes everything fresh.</p>
                </div>
                
                <div class="feature-card">
                    <h3>🚨 EMERGENCY STOP</h3>
                    <p>Immediate halt of all trading activities. Use when urgent intervention is required.</p>
                </div>
            </div>
        </div>

        <div class="system-requirements">
            <h3>📋 System Requirements</h3>
            <ul style="margin-left: 20px;">
                <li><strong>Mobile Device:</strong> Any smartphone or tablet</li>
                <li><strong>Browser:</strong> Chrome, Safari, Firefox, Edge, or DuckDuckGo</li>
                <li><strong>Internet:</strong> Required for installation and real-time updates</li>
                <li><strong>Storage:</strong> Minimal - works offline after installation</li>
                <li><strong>Network:</strong> Access to http://*************:8000</li>
            </ul>
        </div>

        <div class="installation-section">
            <h2>⚡ Quick Actions</h2>
            <div style="text-align: center;">
                <a href="http://*************:8000/mobile" class="install-btn">📱 Install Mobile App</a>
                <a href="http://*************:8000/control" class="install-btn secondary">🎯 Open Control Panel</a>
                <a href="http://*************:8000/status" class="install-btn">📊 Check System Status</a>
            </div>
        </div>

        <div class="important-note">
            <h4>💡 Pro Tip</h4>
            <p>Bookmark http://*************:8000/control for instant access to system controls from any device. The system operates independently - use the mobile app for convenience, not necessity.</p>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
    <script>
        // Generate QR code for mobile control
        const qrText = 'http://*************:8000/mobile';
        
        QRCode.toCanvas(document.getElementById('qr-control'), qrText, {
            width: 200,
            margin: 2,
            color: {
                dark: '#000000',
                light: '#FFFFFF'
            }
        }, function (error) {
            if (error) console.error(error);
            console.log('QR code generated successfully');
        });

        // Add click handlers for immediate actions
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Permanent Mobile App Installation Guide Loaded');
            
            // Check if we're on mobile
            const isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
            
            if (isMobile) {
                // Add special styling for mobile devices
                document.body.style.padding = '10px';
                
                // Show install prompt
                setTimeout(() => {
                    if (confirm('Would you like to install the Trading Control App to your home screen?')) {
                        window.location.href = 'http://*************:8000/mobile';
                    }
                }, 2000);
            }
        });
    </script>
</body>
</html>
