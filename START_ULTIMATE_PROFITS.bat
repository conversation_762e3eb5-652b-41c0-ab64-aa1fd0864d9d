@echo off
echo ========================================
echo ULTIMATE PROFIT SYSTEM LAUNCHER
echo ========================================
echo Starting the ONE entry point for ALL functions and REAL profit generation
echo.

cd /d "e:\The_real_deal_copy\Bybit_Bot\BOT"

echo [INFO] Activating conda environment...
call E:\conda\miniconda3\Scripts\conda.exe activate C:\Users\<USER>\.conda\envs\bybit-trader

echo [INFO] Starting ULTIMATE PROFIT SYSTEM...
python ULTIMATE_PROFIT_SYSTEM.py

echo.
echo [INFO] Ultimate Profit System has stopped
pause
