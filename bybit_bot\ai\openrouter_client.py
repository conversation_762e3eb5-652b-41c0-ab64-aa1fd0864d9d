"""
OpenRouter AI Client Integration
Provides access to advanced AI models through OpenRouter API
"""

import os
import json
import asyncio
import aiohttp
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from datetime import datetime

from ..core.config import BotConfig
from ..core.logger import TradingBotLogger


@dataclass
class OpenRouterModel:
    """OpenRouter Model Configuration"""
    id: str
    name: str
    pricing: Dict[str, float]
    context_length: int
    architecture: str
    modality: str
    top_provider: Dict[str, Any]


class OpenRouterClient:
    """
    OpenRouter API Client for accessing advanced AI models
    Supports Claude Opus 4, GPT-4, and other premium models
    """
    
    def __init__(self, config: BotConfig):
        self.config = config
        self.logger = TradingBotLogger("OpenRouterClient")
        
        # OpenRouter Configuration
        self.base_url = "https://openrouter.ai/api/v1"
        
        # Get API key from config or environment (optional)
        self.api_key = (
            os.getenv("OPENROUTER_API_KEY") or 
            getattr(config, 'openrouter_api_key', None)
        )
        
        # Check if API key is available - make it optional for SuperGPT
        if not self.api_key:
            self.api_key = "demo_key_for_testing"  # Use demo key as fallback
            self.logger.warning("OpenRouter API key not found - using demo mode")
        
        # Site configuration - use simple defaults
        self.site_url = "https://bybit-trading-bot.local"
        self.site_name = "Bybit Trading Bot"
        
        # Model preferences - use defaults
        self.preferred_models = [
            "anthropic/claude-3.5-sonnet",
            "openai/gpt-4-turbo",
            "openai/gpt-4",
            "google/gemini-pro-1.5"
        ]
        
        # Session management
        self.session = None
        self.available_models = {}
        
        # Rate limiting
        self.rate_limit_delay = 1.0
        self.last_request_time = 0
        
    async def initialize(self):
        """Initialize OpenRouter client"""
        try:
            self.logger.info("Initializing OpenRouter client...")
            
            if not self.api_key or self.api_key == "demo_key_for_testing":
                self.logger.warning("OpenRouter running in demo mode - limited functionality")
                return True  # Continue without full initialization
            
            # Create aiohttp session
            self.session = aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=60)
            )
            
            # Load available models
            await self.load_available_models()
            
            self.logger.info(f"OpenRouter client initialized with {len(self.available_models)} models")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize OpenRouter client: {e}")
            raise
    
    async def load_available_models(self):
        """Load available models from OpenRouter"""
        try:
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            async with self.session.get(f"{self.base_url}/models", headers=headers) as response:
                if response.status == 200:
                    data = await response.json()
                    for model_data in data.get("data", []):
                        model = OpenRouterModel(
                            id=model_data["id"],
                            name=model_data["name"],
                            pricing=model_data.get("pricing", {}),
                            context_length=model_data.get("context_length", 4096),
                            architecture=model_data.get("architecture", {}).get("tokenizer", "unknown"),
                            modality=model_data.get("architecture", {}).get("modality", "text"),
                            top_provider=model_data.get("top_provider", {})
                        )
                        self.available_models[model.id] = model
                else:
                    self.logger.warning(f"Failed to load models: {response.status}")
                    
        except Exception as e:
            self.logger.error(f"Error loading available models: {e}")
    
    async def get_completion(self, 
                           messages: List[Dict[str, Any]],
                           model: str = "anthropic/claude-opus-4",
                           temperature: float = 0.7,
                           max_tokens: Optional[int] = None,
                           stream: bool = False) -> Dict[str, Any]:
        """Get completion from OpenRouter model"""
        try:
            # Rate limiting
            await self._enforce_rate_limit()
            
            # Prepare request
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json",
                "HTTP-Referer": self.site_url,
                "X-Title": self.site_name
            }
            
            data = {
                "model": model,
                "messages": messages,
                "temperature": temperature,
                "stream": stream
            }
            
            if max_tokens:
                data["max_tokens"] = max_tokens
            
            # Make request
            async with self.session.post(
                f"{self.base_url}/chat/completions",
                headers=headers,
                json=data
            ) as response:
                
                if response.status == 200:
                    result = await response.json()
                    return result
                else:
                    error_text = await response.text()
                    self.logger.error(f"OpenRouter API error: {response.status} - {error_text}")
                    return {"error": f"API error: {response.status}"}
                    
        except Exception as e:
            self.logger.error(f"Error getting completion: {e}")
            return {"error": str(e)}
    
    async def analyze_trading_context(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze trading context using Claude Opus 4"""
        try:
            messages = [
                {
                    "role": "system",
                    "content": """You are an expert cryptocurrency trading analyst with advanced market analysis capabilities. 
                    Analyze the provided trading context and provide detailed insights, recommendations, and risk assessments.
                    Focus on actionable trading strategies and market opportunities."""
                },
                {
                    "role": "user",
                    "content": f"Analyze this trading context and provide detailed insights:\n\n{json.dumps(context, indent=2)}"
                }
            ]
            
            result = await self.get_completion(
                messages=messages,
                model="anthropic/claude-opus-4",
                temperature=0.3,
                max_tokens=2000
            )
            
            if "error" not in result:
                analysis = result["choices"][0]["message"]["content"]
                return {
                    "analysis": analysis,
                    "model_used": "anthropic/claude-opus-4",
                    "timestamp": datetime.utcnow().isoformat(),
                    "tokens_used": result.get("usage", {})
                }
            else:
                return result
                
        except Exception as e:
            self.logger.error(f"Error analyzing trading context: {e}")
            return {"error": str(e)}
    
    async def generate_trading_strategy(self, market_data: Dict[str, Any]) -> Dict[str, Any]:
        """Generate trading strategy using advanced AI"""
        try:
            messages = [
                {
                    "role": "system",
                    "content": """You are a quantitative trading strategist specializing in cryptocurrency markets.
                    Generate sophisticated trading strategies based on market data analysis.
                    Include specific entry/exit points, risk management, and position sizing."""
                },
                {
                    "role": "user",
                    "content": f"Generate a comprehensive trading strategy based on this market data:\n\n{json.dumps(market_data, indent=2)}"
                }
            ]
            
            result = await self.get_completion(
                messages=messages,
                model="anthropic/claude-opus-4",
                temperature=0.4,
                max_tokens=2500
            )
            
            if "error" not in result:
                strategy = result["choices"][0]["message"]["content"]
                return {
                    "strategy": strategy,
                    "model_used": "anthropic/claude-opus-4",
                    "timestamp": datetime.utcnow().isoformat(),
                    "confidence": 0.85,
                    "tokens_used": result.get("usage", {})
                }
            else:
                return result
                
        except Exception as e:
            self.logger.error(f"Error generating trading strategy: {e}")
            return {"error": str(e)}
    
    async def risk_assessment(self, trade_proposal: Dict[str, Any]) -> Dict[str, Any]:
        """Perform risk assessment using AI"""
        try:
            messages = [
                {
                    "role": "system",
                    "content": """You are a risk management expert for cryptocurrency trading.
                    Analyze trade proposals and provide comprehensive risk assessments.
                    Include risk scores, potential downsides, and mitigation strategies."""
                },
                {
                    "role": "user",
                    "content": f"Perform a detailed risk assessment for this trade proposal:\n\n{json.dumps(trade_proposal, indent=2)}"
                }
            ]
            
            result = await self.get_completion(
                messages=messages,
                model="anthropic/claude-3.5-sonnet",  # Good for risk analysis
                temperature=0.2,
                max_tokens=1500
            )
            
            if "error" not in result:
                assessment = result["choices"][0]["message"]["content"]
                return {
                    "risk_assessment": assessment,
                    "model_used": "anthropic/claude-3.5-sonnet",
                    "timestamp": datetime.utcnow().isoformat(),
                    "tokens_used": result.get("usage", {})
                }
            else:
                return result
                
        except Exception as e:
            self.logger.error(f"Error performing risk assessment: {e}")
            return {"error": str(e)}
    
    async def market_sentiment_analysis(self, news_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Analyze market sentiment from news data"""
        try:
            news_text = "\n".join([
                f"Title: {item.get('title', '')}\nContent: {item.get('content', '')[:500]}"
                for item in news_data[:10]  # Limit to 10 most recent
            ])
            
            messages = [
                {
                    "role": "system",
                    "content": """You are a market sentiment analyst specializing in cryptocurrency markets.
                    Analyze news and social media content to determine overall market sentiment.
                    Provide sentiment scores and trading implications."""
                },
                {
                    "role": "user",
                    "content": f"Analyze the sentiment of this market news and provide trading insights:\n\n{news_text}"
                }
            ]
            
            result = await self.get_completion(
                messages=messages,
                model="openai/gpt-4-turbo",
                temperature=0.3,
                max_tokens=1200
            )
            
            if "error" not in result:
                sentiment = result["choices"][0]["message"]["content"]
                return {
                    "sentiment_analysis": sentiment,
                    "model_used": "openai/gpt-4-turbo",
                    "timestamp": datetime.utcnow().isoformat(),
                    "tokens_used": result.get("usage", {})
                }
            else:
                return result
                
        except Exception as e:
            self.logger.error(f"Error analyzing market sentiment: {e}")
            return {"error": str(e)}
    
    async def get_model_info(self, model_id: str) -> Optional[OpenRouterModel]:
        """Get information about a specific model"""
        return self.available_models.get(model_id)
    
    async def list_available_models(self) -> List[str]:
        """List all available model IDs"""
        return list(self.available_models.keys())
    
    async def _enforce_rate_limit(self):
        """Enforce rate limiting between requests"""
        current_time = asyncio.get_event_loop().time()
        time_since_last = current_time - self.last_request_time
        
        if time_since_last < self.rate_limit_delay:
            await asyncio.sleep(self.rate_limit_delay - time_since_last)
        
        self.last_request_time = asyncio.get_event_loop().time()
    
    async def shutdown(self):
        """Shutdown OpenRouter client"""
        if self.session:
            await self.session.close()
            self.logger.info("OpenRouter client shutdown complete")


# Convenience function for quick access
async def create_openrouter_client(config: BotConfig) -> OpenRouterClient:
    """Create and initialize OpenRouter client"""
    client = OpenRouterClient(config)
    await client.initialize()
    return client
