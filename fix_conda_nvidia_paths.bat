@echo off
REM ============================================================================
REM COMPREHENSIVE E-DRIVE CONDA & PYTHON SETUP - NVIDIA PATH FIX
REM ============================================================================
REM This script completely fixes conda activation and NVIDIA path conflicts
REM ============================================================================

title E-Drive Conda & Python Setup - NVIDIA Path Fix

echo.
echo ================================================================================
echo COMPREHENSIVE E-DRIVE CONDA & PYTHON SETUP - NVIDIA PATH FIX
echo ================================================================================
echo.

REM Clear problematic environment variables
set "NVIDIA_PATH_BACKUP=%PATH%"
for /f "tokens=1* delims=;" %%A in ("%PATH%") do (
    set "PATH_TEMP=%%A"
    if not "%%B"=="" set "PATH_TEMP=!PATH_TEMP!;%%B"
)

REM Remove NVIDIA paths that cause conflicts
set "PATH=%PATH:C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.1\bin;=%"
set "PATH=%PATH:C:\Program Files\NVIDIA GPU Computing Toolkit\CUDA\v12.1\libnvvp;=%"
set "PATH=%PATH:C:\Program Files\NVIDIA Corporation\Nsight Compute 2023.1.1\;=%"

REM Set clean E-drive paths first
set "CONDA_ROOT=E:\conda\miniconda3"
set "CONDA_ENV_PATH=E:\conda\envs\bybit-trader"
set "PYTHON_EXE=E:\conda\envs\bybit-trader\python.exe"
set "PIP_EXE=E:\conda\envs\bybit-trader\Scripts\pip.exe"

REM Set environment variables for this session
set "CONDA_DEFAULT_ENV=bybit-trader"
set "CONDA_PREFIX=%CONDA_ENV_PATH%"
set "CONDA_PYTHON_EXE=%PYTHON_EXE%"
set "PIP_CACHE_DIR=E:\conda\pip_cache"
set "PYTHONPATH=E:\The_real_deal_copy\Bybit_Bot\BOT"
set "BYBIT_BOT_HOME=E:\The_real_deal_copy\Bybit_Bot\BOT"
set "TEMP=E:\temp"
set "TMP=E:\temp"
set "TMPDIR=E:\temp"

REM Update PATH to prioritize E-drive environment (BEFORE any other paths)
set "PATH=%CONDA_ENV_PATH%;%CONDA_ENV_PATH%\Scripts;%CONDA_ENV_PATH%\Library\bin;%CONDA_ROOT%\condabin;%PATH%"

REM Create necessary directories
if not exist "E:\temp" mkdir "E:\temp"
if not exist "E:\conda\pip_cache" mkdir "E:\conda\pip_cache"

echo [OK] Environment variables set
echo [OK] NVIDIA paths cleaned
echo [OK] E-drive paths prioritized

REM Verify paths exist
if not exist "%PYTHON_EXE%" (
    echo [ERROR] Python executable not found at %PYTHON_EXE%
    pause
    exit /b 1
)

if not exist "%PIP_EXE%" (
    echo [ERROR] Pip executable not found at %PIP_EXE%
    pause
    exit /b 1
)

echo [SUCCESS] All E-drive components verified
echo Python: %PYTHON_EXE%
echo Pip: %PIP_EXE%
echo Working Directory: %BYBIT_BOT_HOME%

REM Test Python directly (no conda activate needed)
echo.
echo [INFO] Testing Python environment...
"%PYTHON_EXE%" --version
if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] Python test failed
    pause
    exit /b 1
)

echo.
echo [SUCCESS] E-drive Python environment is ready!
echo [INFO] No conda activate needed - using direct Python paths
echo [INFO] Ready to run trading bot with: "%PYTHON_EXE%" main_unified_system.py
echo.
