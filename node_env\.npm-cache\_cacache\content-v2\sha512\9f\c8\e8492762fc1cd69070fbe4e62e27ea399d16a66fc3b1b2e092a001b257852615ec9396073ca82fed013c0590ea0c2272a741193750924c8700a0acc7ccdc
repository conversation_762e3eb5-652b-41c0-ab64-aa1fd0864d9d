{"_id": "@types/connect", "_rev": "638-589379c49c42f882dbce059bc2aeb732", "name": "@types/connect", "dist-tags": {"ts2.0": "3.4.32", "ts2.1": "3.4.32", "ts2.2": "3.4.32", "ts2.3": "3.4.32", "ts2.4": "3.4.32", "ts2.5": "3.4.32", "ts2.6": "3.4.32", "ts2.7": "3.4.32", "ts2.8": "3.4.33", "ts2.9": "3.4.33", "ts3.0": "3.4.33", "ts3.1": "3.4.33", "ts3.2": "3.4.33", "ts3.3": "3.4.34", "ts3.4": "3.4.34", "ts3.5": "3.4.34", "ts3.6": "3.4.35", "ts3.7": "3.4.35", "ts3.8": "3.4.35", "ts3.9": "3.4.35", "ts4.0": "3.4.35", "ts4.1": "3.4.35", "ts4.2": "3.4.35", "ts4.3": "3.4.36", "ts4.4": "3.4.36", "ts5.8": "3.4.38", "ts5.7": "3.4.38", "latest": "3.4.38", "ts4.5": "3.4.38", "ts4.6": "3.4.38", "ts4.7": "3.4.38", "ts4.8": "3.4.38", "ts4.9": "3.4.38", "ts5.0": "3.4.38", "ts5.1": "3.4.38", "ts5.2": "3.4.38", "ts5.3": "3.4.38", "ts5.4": "3.4.38", "ts5.5": "3.4.38", "ts5.6": "3.4.38", "ts5.9": "3.4.38"}, "versions": {"3.4.16-alpha": {"name": "@types/connect", "version": "3.4.16-alpha", "author": {"name": "Maxime LUCE", "email": "https://github.com/SomaticIT/"}, "license": "MIT", "_id": "@types/connect@3.4.16-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "8b81209d6c5167842fe57bbd6d2b6c5b483272f5", "tarball": "https://registry.npmjs.org/@types/connect/-/connect-3.4.16-alpha.tgz", "integrity": "sha512-V5eft160B2LV4CirHtkCbJhzJUNJ7W/bm0gN4HqrIGqdK0VdCt5YWp5KwKGqVzQssnkrxV4o+H8/X48qrE2g2Q==", "signatures": [{"sig": "MEUCIQCp3FOt+f2WzmcVOitigh+oK2jgViF31F3bViv4TOY3FAIgYwkS+MM6gYFJ16U+iAqPp5C79+ruTAzHrz3fTx6alkY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\connect", "_shasum": "8b81209d6c5167842fe57bbd6d2b6c5b483272f5", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\connect", "_npmVersion": "3.8.2", "description": "Type definitions for connect v3.4.0 from https://www.github.com/DefinitelyTyped/DefinitelyTyped", "directories": {}, "_nodeVersion": "5.5.0", "dependencies": {"http": "*"}, "_npmOperationalInternal": {"tmp": "tmp/connect-3.4.16-alpha.tgz_1463459978602_0.8538189970422536", "host": "packages-12-west.internal.npmjs.com"}}, "3.4.17-alpha": {"name": "@types/connect", "version": "3.4.17-alpha", "author": {"name": "Maxime LUCE", "email": "https://github.com/SomaticIT/"}, "license": "MIT", "_id": "@types/connect@3.4.17-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "12e0c3ebf7ec48f0f7203ba2d6a72e1751b23aff", "tarball": "https://registry.npmjs.org/@types/connect/-/connect-3.4.17-alpha.tgz", "integrity": "sha512-RcpgzTazrmnOGoP4usTswlCpLoSmdFmK96CL12N41cErL5KrS5fJQCCg8zVyZ2i4OTOVkph6DzoDk9vA6oudBw==", "signatures": [{"sig": "MEUCIGWC23k0rNHFz/PJQ+HRagVkJ7Sk4Uz/RjtpEGmsOudLAiEAtb1xKNXWj2Wd5Av4oKO5SsG0zJStGVi5T8ANQY8/7+o=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\connect", "_shasum": "12e0c3ebf7ec48f0f7203ba2d6a72e1751b23aff", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\connect", "_npmVersion": "3.8.2", "description": "Type definitions for connect v3.4.0 from https://www.github.com/DefinitelyTyped/DefinitelyTyped", "directories": {}, "_nodeVersion": "5.5.0", "dependencies": {"http": "*", "@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/connect-3.4.17-alpha.tgz_1463689899002_0.7293500450905412", "host": "packages-16-east.internal.npmjs.com"}}, "3.4.22-alpha": {"name": "@types/connect", "version": "3.4.22-alpha", "author": {"name": "Maxime LUCE", "email": "https://github.com/SomaticIT/"}, "license": "MIT", "_id": "@types/connect@3.4.22-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "79b4271bf3759a921a728486b3aab90ca2ac8296", "tarball": "https://registry.npmjs.org/@types/connect/-/connect-3.4.22-alpha.tgz", "integrity": "sha512-mG932hu3Sr5mTKuVYjPXhjjPFfJrPUYDCj6fNP1aEbWsCNwX/itwGAhke3Ux7hxmRTocMLZtOF8zUecROSktpg==", "signatures": [{"sig": "MEUCIDpuqT1tmAMafs893kkYvpdPNLgE0cNv+QfTdA1U6PB/AiEAuezC1WVAN/3VJOYPxUMmmJdDz80sqX7Jc49rkRQwoKk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\connect", "_shasum": "79b4271bf3759a921a728486b3aab90ca2ac8296", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\connect", "_npmVersion": "3.8.2", "description": "TypeScript definitions for connect v3.4.0", "directories": {}, "_nodeVersion": "5.5.0", "dependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/connect-3.4.22-alpha.tgz_1463772076307_0.6095243310555816", "host": "packages-12-west.internal.npmjs.com"}}, "3.4.23-alpha": {"name": "@types/connect", "version": "3.4.23-alpha", "author": {"name": "Maxime LUCE", "email": "https://github.com/SomaticIT/"}, "license": "MIT", "_id": "@types/connect@3.4.23-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "d6821100c13d06b9a90d31535fbbbdd401c57537", "tarball": "https://registry.npmjs.org/@types/connect/-/connect-3.4.23-alpha.tgz", "integrity": "sha512-khN4Ihx/eJRRS8ORjVs2JXwCDaG8EMYT6/ph1idThDO+FcIFjmaMvTHOiMWh3CI1EdbUZnQ849TNuoyXJwe2eg==", "signatures": [{"sig": "MEQCIGs8931uRGvrphLMrr35Z4G1GG21w5rHwQNGXls9Ny3LAiA35mR7pv3LHhExWbzspUjMShP70M/wvOfw+Kznrphj6w==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\connect", "_shasum": "d6821100c13d06b9a90d31535fbbbdd401c57537", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\connect", "_npmVersion": "3.8.2", "description": "TypeScript definitions for connect v3.4.0", "directories": {}, "_nodeVersion": "5.5.0", "dependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/connect-3.4.23-alpha.tgz_1464151166325_0.7953211506828666", "host": "packages-16-east.internal.npmjs.com"}}, "3.4.24-alpha": {"name": "@types/connect", "version": "3.4.24-alpha", "author": {"name": "Maxime LUCE", "email": "https://github.com/SomaticIT/"}, "license": "MIT", "_id": "@types/connect@3.4.24-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "c54659901c646906946a4c3369c1912002887073", "tarball": "https://registry.npmjs.org/@types/connect/-/connect-3.4.24-alpha.tgz", "integrity": "sha512-P0BKDoEVifFyKV/4KX8hkASg4sgtiYCMtfeqnto+xyiDS7cdxd3KPDyOYMEBhCl8HNMqTuiE+qyI5sQGR6Z6Xw==", "signatures": [{"sig": "MEUCIQD6R21tCQik2bqOjIYdQgG/mME+Sil+yRK6AZht+Et9OwIgAUiQRIkiAF9xr76iSL68t96KNfymJHjpwDXahhQZ+t8=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\connect", "_shasum": "c54659901c646906946a4c3369c1912002887073", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\connect", "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "_npmVersion": "3.9.5", "description": "TypeScript definitions for connect v3.4.0", "directories": {}, "_nodeVersion": "6.2.2", "dependencies": {"@types/node": "4.0.*"}, "_npmOperationalInternal": {"tmp": "tmp/connect-3.4.24-alpha.tgz_1467399933945_0.07637611869722605", "host": "packages-12-west.internal.npmjs.com"}}, "3.4.25-alpha": {"name": "@types/connect", "version": "3.4.25-alpha", "author": {"name": "Maxime LUCE", "email": "https://github.com/SomaticIT/"}, "license": "MIT", "_id": "@types/connect@3.4.25-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "80f18d961c79bb6a177af2a0acac01cff158c9ca", "tarball": "https://registry.npmjs.org/@types/connect/-/connect-3.4.25-alpha.tgz", "integrity": "sha512-vC/Yu/pZAlZdc4xhM0Z6tmwzWl/znlTy0eiVHbLIb8TG5vVSLLP1pTvl+Nk9X2s9nbYJDOizwdub6CrnUDpQpw==", "signatures": [{"sig": "MEUCIBfP2+PlYuo+dw7zQ1OErGn7Ef2bIcWHEKa7J7X2dYHtAiEA8alqnM2Frc6T+NTk3lv8RjawRaCHbQAr6KKFJK27VKQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\connect", "_shasum": "80f18d961c79bb6a177af2a0acac01cff158c9ca", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\connect", "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "_npmVersion": "3.7.2", "description": "TypeScript definitions for connect v3.4.0", "directories": {}, "_nodeVersion": "6.2.1", "dependencies": {"@types/node": "4.0.23-alpha"}, "_npmOperationalInternal": {"tmp": "tmp/connect-3.4.25-alpha.tgz_1467411985886_0.36869155149906874", "host": "packages-12-west.internal.npmjs.com"}}, "3.4.26-alpha": {"name": "@types/connect", "version": "3.4.26-alpha", "author": {"name": "Maxime LUCE", "email": "https://github.com/SomaticIT/"}, "license": "MIT", "_id": "@types/connect@3.4.26-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "733e5cf76e575c7ca1a71d48a3bf0b9092b85691", "tarball": "https://registry.npmjs.org/@types/connect/-/connect-3.4.26-alpha.tgz", "integrity": "sha512-05pz5+3KNbkqnQLuG2k6vI9wHWfx6re1pBTTL5l/7yk6lxICe8mLqlB4YrNT5Qt78XRzM+u+wscZ8zNBAlZ2eA==", "signatures": [{"sig": "MEUCIQChArhCLliLe4NTY4QxD5jTiRjHvNsJXJFoR8YIBcL/2QIgZPrMtOl513tgrTkpLpEGIMK4CsBE0KYQgW7ZpXnJRnI=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\connect", "_shasum": "733e5cf76e575c7ca1a71d48a3bf0b9092b85691", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\connect", "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "_npmVersion": "3.7.2", "description": "TypeScript definitions for connect v3.4.0", "directories": {}, "_nodeVersion": "6.2.1", "dependencies": {"@types/node": "4.0.24-alpha"}, "_npmOperationalInternal": {"tmp": "tmp/connect-3.4.26-alpha.tgz_1467425382944_0.46205302281305194", "host": "packages-16-east.internal.npmjs.com"}}, "3.4.27-alpha": {"name": "@types/connect", "version": "3.4.27-alpha", "author": {"name": "Maxime LUCE", "email": "https://github.com/SomaticIT/"}, "license": "MIT", "_id": "@types/connect@3.4.27-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "970bf64345a3c547a939cc5ae1af38819954d04d", "tarball": "https://registry.npmjs.org/@types/connect/-/connect-3.4.27-alpha.tgz", "integrity": "sha512-lKpwrBWd5Mgc4FLVO5oscMA1RdyfZg3zvhDmprGRD1t22PKAoZABS20RNiAGLmikB1QZSCo3b5HVgr3ffxbfZQ==", "signatures": [{"sig": "MEUCIQDYMZbNSy5Uo2HruSFarOqDI3vhqGLyMu7vAtKqczbsdwIgSPe8DrX0mBk1bnM+Zj35qOZJTBraQ2y+yh5Rn13xvfk=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\connect", "_shasum": "970bf64345a3c547a939cc5ae1af38819954d04d", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\connect", "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "_npmVersion": "3.7.2", "description": "TypeScript definitions for connect v3.4.0", "directories": {}, "_nodeVersion": "6.2.1", "dependencies": {"@types/node": "4.0.26-alpha"}, "_npmOperationalInternal": {"tmp": "tmp/connect-3.4.27-alpha.tgz_1467590302440_0.4710211739875376", "host": "packages-16-east.internal.npmjs.com"}}, "3.4.28-alpha": {"name": "@types/connect", "version": "3.4.28-alpha", "author": {"name": "Maxime LUCE", "email": "https://github.com/SomaticIT/"}, "license": "MIT", "_id": "@types/connect@3.4.28-alpha", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "d964415f107dd5b5ec4aa38db17e4d792950d260", "tarball": "https://registry.npmjs.org/@types/connect/-/connect-3.4.28-alpha.tgz", "integrity": "sha512-X6Ul0xDFBjvwAYYAJyccYAqdDjOyg5Wq5hKJS1dRpJAsQ6c3CK3pshCNZMDhCvetUiPfUxKP3NWoweM0PTg53w==", "signatures": [{"sig": "MEUCIAjcy7dUR/y2uQBgP/RxTr7i9yToNIjsGSzH0ReHUnCLAiEA3vT8wHUNDcq+XbGttzLl7sG/tvMcQaAatmAsd46poZ4=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\connect", "_shasum": "d964415f107dd5b5ec4aa38db17e4d792950d260", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\connect", "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "TypeScript definitions for connect v3.4.0", "directories": {}, "_nodeVersion": "6.3.0", "dependencies": {"@types/node": "4.0.27-alpha"}, "_npmOperationalInternal": {"tmp": "tmp/connect-3.4.28-alpha.tgz_1468007477590_0.7176749978680164", "host": "packages-16-east.internal.npmjs.com"}}, "3.4.29": {"name": "@types/connect", "version": "3.4.29", "author": {"name": "Maxime LUCE", "email": "https://github.com/SomaticIT/"}, "license": "MIT", "_id": "@types/connect@3.4.29", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "73cd70f19f82ffd2db2151d18c07c210ba7b9a4b", "tarball": "https://registry.npmjs.org/@types/connect/-/connect-3.4.29.tgz", "integrity": "sha512-gzS12LHDgenF40KAgTI+xg6TkB2FjuoaBFMNzS1oLdvwHbsht38ZN32rwafn7+iA7F7fgfAHYapMd7m/AlNXjA==", "signatures": [{"sig": "MEYCIQCNArI2oe0ZinCLdPsGjF2EVjME/+UdWd4gdocRQ66M+QIhAIspnxcLfxYExhWtUIaiiuJDGgXUSJZR0fnbr8kAwn94", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "_from": "output\\connect", "_shasum": "73cd70f19f82ffd2db2151d18c07c210ba7b9a4b", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "_resolved": "file:output\\connect", "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "_npmVersion": "3.10.3", "description": "TypeScript definitions for connect v3.4.0", "directories": {}, "_nodeVersion": "6.3.0", "dependencies": {"@types/node": "4.0.*"}, "_npmOperationalInternal": {"tmp": "tmp/connect-3.4.29.tgz_1468505918371_0.9459750363603234", "host": "packages-12-west.internal.npmjs.com"}}, "3.4.30": {"name": "@types/connect", "version": "3.4.30", "author": "Maxime LUCE <https://github.com/SomaticIT/>", "license": "MIT", "_id": "@types/connect@3.4.30", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "dist": {"shasum": "0a8ede309ce0ebdba2f0534e574692511dd71eb6", "tarball": "https://registry.npmjs.org/@types/connect/-/connect-3.4.30.tgz", "integrity": "sha512-IryvM4ljwDI9BqK+3jrqbyFRrrbhC6u24ajCuB5pFQPJr05mk6Ccd5nRZE+tI4dCHBAHZgW7blGBQpEOEFn7MQ==", "signatures": [{"sig": "MEUCIBUprrUy2/12wtSU9Rk9QgiNg0g4JD+mJs+E+H9x+/+jAiEA5kKAD0eHDi3LpYznxwclof8hjiyU+BdxwbCypybsqmQ=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "typings": "index.d.ts", "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for connect v3.4.0", "directories": {}, "dependencies": {"@types/node": "*"}, "_npmOperationalInternal": {"tmp": "tmp/connect-3.4.30.tgz_1474302846425_0.122144729597494", "host": "packages-16-east.internal.npmjs.com"}, "typesPublisherContentHash": "199ef730a9375bbde32e7d43e3509db3e94ee5e2cf2f8fa186d98e35944db4fa"}, "3.4.31": {"name": "@types/connect", "version": "3.4.31", "license": "MIT", "_id": "@types/connect@3.4.31", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/SomaticIT", "name": "Maxime LUCE"}], "dist": {"shasum": "1f92d6b117ecc05076c49ecd024f7976e528bad9", "tarball": "https://registry.npmjs.org/@types/connect/-/connect-3.4.31.tgz", "integrity": "sha512-OPSxsP6XqA3984KWDUXq/u05Hu8VWa/2rUVlw/aDUOx87BptIep6xb3NdCxCpKLfLdjZcCE5jR+gouTul3gjdA==", "signatures": [{"sig": "MEQCIEHms4b109k2ipw1q+QXfQ/4u1sOSJeGgJA+YNyJY8c4AiBdNPXeoDjiUAImkv+BVHRJAkw0y1YLyWcSWdpLAS9xDw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}]}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for connect", "directories": {}, "dependencies": {"@types/node": "*"}, "typeScriptVersion": "2.0", "_npmOperationalInternal": {"tmp": "tmp/connect-3.4.31.tgz_1503352214317_0.1901183445006609", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "6a57450806ce3c8be2bdd714e3a8a77f261eb70203dd67b0b8185a98dedbf713"}, "3.4.32": {"name": "@types/connect", "version": "3.4.32", "license": "MIT", "_id": "@types/connect@3.4.32", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/SomaticIT", "name": "Maxime LUCE", "githubUsername": "SomaticIT"}, {"url": "https://github.com/EvanHahn", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}], "dist": {"shasum": "aa0e9616b9435ccad02bc52b5b454ffc2c70ba28", "tarball": "https://registry.npmjs.org/@types/connect/-/connect-3.4.32.tgz", "fileCount": 4, "integrity": "sha512-4r8qa0quOvh7lGD0pre62CAb1oni1OO6ecJLGCezTmhQ8Fz50Arx9RUszryR8KlgK6avuSXvviL6yWyViQABOg==", "signatures": [{"sig": "MEYCIQClhitFivdHSHdZwn+HBanoIOPD+17XpgK4QCPl3QZruAIhANsiEY8Dw1ePrA87Z/9g8asqP0pukO2mFY67e+IEmetu", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5943, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJa1UeOCRA9TVsSAnZWagAAODMP/0qMGYMX+yUeUjBFgtma\nb7CEsMYdER/zwzln/ApOIgcRUrTi+efC9/mx8y99ajynW4Ned5HxRpC4JprV\n6Waz4vyaEL5Kyhpm1cBAhDoQGWNixjCLMWPGzmz5LDaBkjeEjgr18BuLvQuC\npKRQMnirEzu5Xq5Vx6kAnY7zZr6rRSWqUrXwmn/KaPEuY6AQe2mXrcO/V42o\nJwuWVzn313ExHutvDopQXZ3bq0OTiT6i+iSkTERe/FCUKMS07Ophh9V4L8PH\n/bFXG7fCA1Z1Y/7vn79X9sJHbAMkOO10JVKNfloPngecgpxAtzVjj6UEM6wi\nHJsV1itz5u+5JN8fcwv37L8VUuIoq+Uhy0zsN12BctpWAwu8BLKWOQp6qZht\nPJ1ViZRku3Xo4yISWPIc5sV0HZ+BquK4NqrpTzGSoU1VR+tPHwIu+1V/8Sme\nU0K9eT6IYr62OBG6zb8NoXmwKnpV5YmRsoPUZGab63ocZwZ8ZJkWpplwffi3\nQLRiy3br01Mha52r8iHnJQt8dncN2RXwr4+9Y5H0n5K1utDGi2Qkw3d0cv81\ny+/WlaeUGKqy8avTZ09GZp1yehiqLoe7gRUiodZ4cbrUeL0NBqlONhq//yfK\nfNjLXo7nl2Zx6JrCJvu7uai351nJeWwZI1Lf+ECbv9eoL4q/hXpbHqgajdtO\nhBJq\r\n=Irxg\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://www.github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git"}, "description": "TypeScript definitions for connect", "directories": {}, "dependencies": {"@types/node": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.0", "_npmOperationalInternal": {"tmp": "tmp/connect_3.4.32_1523926925440_0.4914644682921856", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "d988cf1d88b273d694c4fcfe3699c4785f7e60ea0c8e094b598922c43ab3fb4e"}, "3.4.33": {"name": "@types/connect", "version": "3.4.33", "license": "MIT", "_id": "@types/connect@3.4.33", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/SomaticIT", "name": "Maxime LUCE", "githubUsername": "SomaticIT"}, {"url": "https://github.com/EvanHahn", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}], "dist": {"shasum": "31610c901eca573b8713c3330abc6e6b9f588546", "tarball": "https://registry.npmjs.org/@types/connect/-/connect-3.4.33.tgz", "fileCount": 4, "integrity": "sha512-2+FrkXY4zllzTNfJth7jOqEHC+enpLeGslEhpnTAkg21GkRrWV4SsAtqchtT4YS9/nODBU2/ZfsBY2X4J/dX7A==", "signatures": [{"sig": "MEUCIG9+vzxNHR9JCWm71mnu+meNEDwsHqOnVAMo02/AlmEqAiEA3MTNovgo51j66qmz2B+sFNGyQ18OcLCIerRPANpFpXY=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6064, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.4\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJd+NfoCRA9TVsSAnZWagAAAd8P/38VfyCyF6fIqz+qtjh9\nh6MkJjhnReYcY8nnsgpNy3vGU8NCT5wABho73dok1IUvpwYt0MzSPR1YN9gw\nak729XXSodKA+IR6biNweNjNaNH0QyCNDlikOLnZPjNoUqrTFveHCBTArzIt\nUJ7K3vWklkU+ZsE8oRG9QyMt8Zepl13mewgKx8MVNY0yfwXWdY6GQ3eHBIni\nBJstM8OIXlcUXBTyVCHoAKHJmscbCAeJSnSBkJdp2WVsv3ClfTBeweH321xD\nQuT/a97y7g1dnU1znDVl9zZcQHMeoSAEkWQ7sBjNnDDnr5Z8CA2thHI54ENt\nwfiZBSJCKqIFOSuJJLK4T4oLvnPW1IZbgP6Ys+5GRfyUUQmV2/aAptQqV5vK\nrZbKoUCMjTHJmXf9FU5bv3XR0PDwY2SUwWWq89GPjCRMgVByXUxc7dIHp5IV\nzEfl+tnJLF7tZCxhjazr6/T9pdJpxovYPCwS3Gi9mDoYsdVI7NPFXY6WtCZD\nsrmk7eYWAeB2EnGte3sMwmSh+1op5rVtGG1GEevnkE9BNQTGG/5pO/3rW87N\nHE8o7EfZN0HujJ4AP/c2P2pESCr8WhgLTIJ69nsV2itMy8dveiNZkcIY1nMD\nE2+pfNRt2SlimHS9Rx2cC4nU48rzeXBZ560yEp+5BlQu12DE4KVpHldX9Pz0\nUPjj\r\n=C47q\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/connect"}, "description": "TypeScript definitions for connect", "directories": {}, "dependencies": {"@types/node": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "2.8", "_npmOperationalInternal": {"tmp": "tmp/connect_3.4.33_1576589288554_0.9701462995828489", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "d19c4861b6d43a27e57437c43e111f73cb2ba46b364d4f737a8ba379b4f4eddb"}, "3.4.34": {"name": "@types/connect", "version": "3.4.34", "license": "MIT", "_id": "@types/connect@3.4.34", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/SomaticIT", "name": "Maxime LUCE", "githubUsername": "SomaticIT"}, {"url": "https://github.com/EvanHahn", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}], "dist": {"shasum": "170a40223a6d666006d93ca128af2beb1d9b1901", "tarball": "https://registry.npmjs.org/@types/connect/-/connect-3.4.34.tgz", "fileCount": 4, "integrity": "sha512-ePPA/JuI+X0vb+gSWlPKOY0NdNAie/rPUqX2GUPpbZwiKTkSPhjXWuee47E4MtE54QVzGCQMQkAL6JhV2E1+cQ==", "signatures": [{"sig": "MEUCIQDrF4wNlDmoVSxBfFunT/5TgqZ9E0veWuIwd0O/IE5MvAIgD7bThSjrtgyPoilO5P8XYCVARY7C6fZUnhKlylE3SBg=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6029, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJfz+XuCRA9TVsSAnZWagAAAsIP/1nu6FdlIMAOQCzJC/Oc\nTrkvCtZXUwbHLMaSYc2xhwABfDiJ5IlBjNy2NrS1V7mzMnnF8TCDptHFdX3O\nQCm0IsRrrplNf8qeNvgxWq8VfAmzMCdhv0+trQOjJZLiSD2XRaIGVLFZsymL\njRwiZJdbJL7MnjeOnZAF5lDS+QEvzAHThpf3CcmGxUjoG35ez4gK96WYZX0o\nngrWFshQhBijtSvImXv+wRK6CFZ+YkrLbE2ikxAqIQEdEv9AQCtQRiDcN9Xd\nJznAE9dCmCazCuaD8ML9oQco4Yh8vRCNxnuECMoA09LJEvOACq7WrNC3eTaa\ns24/SYOt9Q7F/zlnokaNIr50Td4EBe+DEd7EOnmSo0uoxsY1S6OQz75cijm2\n1xrHHGOS4IkxOx8VVGAtl93iVVNNpn1/p9e5DAeF2gjvFMcLIwcy4ojdZF9M\nGiHfkv+c2kdjid698oAQBblEq+jEx7+vYgNgNtjcXaNinY0HZleeyJ0sGXSO\nfdt8u8U5qe5NubvcRyp/z1Zy+Xy8qSFJGlWBEGSTMB1UqZ6bDkm3ws7IBZiS\nVMbTuAGlw0ZDXqlQOkmGfzSzzpsMq0QJ6l5h3QBenKhNg63h3lLcjSu85GXf\nds2VMf2tmay8p3hCzXDnRVwbYT8FYOeh6KBWmlB67FezyZxfDvbnpY5cnMX5\nsmXR\r\n=rB7P\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/connect"}, "description": "TypeScript definitions for connect", "directories": {}, "dependencies": {"@types/node": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.3", "_npmOperationalInternal": {"tmp": "tmp/connect_3.4.34_1607460334042_0.4266442934062529", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "2fd2f6a2f7b9371cdb60b971639b4d26989e6035dc30fb0ad12e72645dcb002d"}, "3.4.35": {"name": "@types/connect", "version": "3.4.35", "license": "MIT", "_id": "@types/connect@3.4.35", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/SomaticIT", "name": "Maxime LUCE", "githubUsername": "SomaticIT"}, {"url": "https://github.com/EvanHahn", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/connect", "dist": {"shasum": "5fcf6ae445e4021d1fc2219a4873cc73a3bb2ad1", "tarball": "https://registry.npmjs.org/@types/connect/-/connect-3.4.35.tgz", "fileCount": 4, "integrity": "sha512-cdeYyv4KWoEgpBISTxWvqYsVy444DOqehiF3fM3ne10AmJ62RSyNkUnxMJXHQWRQQX2eR94m5y1IZyDwBjV9FQ==", "signatures": [{"sig": "MEQCIAsREXZcq6qnvSMQj4z43wLwBEndnJRIddE3e1JpB4CIAiABrWu5Bb121P9op18ZNPmwxYfE5kMPsssecaPQOPJqYQ==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6137, "npm-signature": "-----B<PERSON><PERSON> PGP SIGNATURE-----\r\nVersion: OpenPGP.js v3.0.13\r\nComment: https://openpgpjs.org\r\n\r\nwsFcBAEBCAAQBQJg5L4oCRA9TVsSAnZWagAA1AsQAKORarcnRSLHd2+ZpEun\npXxvAr+FDKdolJpHSiPapkkdYn4PexourIsx9eG2BOobLaXxim+/sS4q4WFQ\n1Gh5D4Q9H/HXw66xhBjI6icS8QDUTAJQJjQWzjLGw0axvj279v3fXL64vkom\nca/6Cgbnip5M3/uM1LufXv+fdsjji05KTcrwPa8TWI5UWIdeHzq7Arl7vgrv\ntcTsYspXIOwv0iUq82cfQ0cP1Hbdf51HplWEPA/V6r3J92EYYGtr217mO75m\njUp5ZEvWFb7AjsZMdgZ9Ef4e+3o53rXP/uQQ7mPThF/DuwY/4U6b5j2Piy5j\n2CHsRBr9VaZUOP7+6Y/HmFQz0vii29Ej5ISC847ODYoFxr2jjKCFy/LBuCU4\nId931H1JWWds/oJ6VFIbZJW5ck9PmaUTQsUBiLKKcYQb9oNPmyiuqW/x1YMQ\nWqRTX26S8hW2Yr3k6oo0i2iGoqlw6EqEvFfAqujq4MLpvVYCQrtvoNjNt6zt\nBO5gT7HEK9p8yI7RLIEE7efOCKSdft0k0aJYEQcD2MKVIdIs06IS0uPmR7jN\n2YCuG9ewAF1KEn9ofYgVPbvoa0knyoHZymHOJtoGtxwC6fi1wz3ZJPQiwVAG\nMBHDXIhibzoIP3qiZB333lLajNezBApG9RLp7KZP16CQ4zGitc31bIgxfMUA\nmFwq\r\n=sPhW\r\n-----END PGP SIGNATURE-----\r\n"}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/connect"}, "description": "TypeScript definitions for connect", "directories": {}, "dependencies": {"@types/node": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "3.6", "_npmOperationalInternal": {"tmp": "tmp/connect_3.4.35_1625603624230_0.3017331323889272", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "09c0dcec5f675cb2bdd7487a85447955f769ef4ab174294478c4f055b528fecc"}, "3.4.36": {"name": "@types/connect", "version": "3.4.36", "license": "MIT", "_id": "@types/connect@3.4.36", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/SomaticIT", "name": "Maxime LUCE", "githubUsername": "SomaticIT"}, {"url": "https://github.com/EvanHahn", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/connect", "dist": {"shasum": "e511558c15a39cb29bd5357eebb57bd1459cd1ab", "tarball": "https://registry.npmjs.org/@types/connect/-/connect-3.4.36.tgz", "fileCount": 5, "integrity": "sha512-P63Zd/JUGq+PdrM1lv0Wv5SBYeA2+CORvbrXbngriYY0jzLUWfQMQQxOhjONEz/wlHOAxOdY7CY65rgQdTjq2w==", "signatures": [{"sig": "MEQCIFk0HzobIfNxTbfe1el4Iihk+W7YuyVaoOygbzp9qaTRAiBJwr5+5mw7qTun/m0xKO8lheg5LaB6hcV4GdQtC9Hgdw==", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 6209}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/connect"}, "description": "TypeScript definitions for connect", "directories": {}, "dependencies": {"@types/node": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.3", "_npmOperationalInternal": {"tmp": "tmp/connect_3.4.36_1693844011954_0.33238658812339006", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "b4e0df138c86cda67415e339a2a397eb7793dcd07875e44a1ebbf084079e8157"}, "3.4.37": {"name": "@types/connect", "version": "3.4.37", "license": "MIT", "_id": "@types/connect@3.4.37", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/SomaticIT", "name": "Maxime LUCE", "githubUsername": "SomaticIT"}, {"url": "https://github.com/EvanHahn", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/connect", "dist": {"shasum": "c66a96689fd3127c8772eb3e9e5c6028ec1a9af5", "tarball": "https://registry.npmjs.org/@types/connect/-/connect-3.4.37.tgz", "fileCount": 5, "integrity": "sha512-zBUSRqkfZ59OcwXon4HVxhx5oWCJmc0OtBTK05M+p0dYjgN6iTwIL2T/WbsQZrEsdnwaF9cWQ+azOnpPvIqY3Q==", "signatures": [{"sig": "MEUCIQDJ2N3Qsh6pwUI+6rPAOdFAK3U3Mvo0C3z556y46pmJUAIgLnb01KRI4yl6I7Yawywk4UgZz4h+rxtOqEyPEnNG7EA=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5907}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/connect"}, "description": "TypeScript definitions for connect", "directories": {}, "dependencies": {"@types/node": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/connect_3.4.37_1697588160208_0.1456757949295353", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "85f03f97bfb82e960080072ae4a2730189121d7207caaec1730ee85f2a53c92b"}, "3.4.38": {"name": "@types/connect", "version": "3.4.38", "license": "MIT", "_id": "@types/connect@3.4.38", "maintainers": [{"name": "types", "email": "<EMAIL>"}], "contributors": [{"url": "https://github.com/SomaticIT", "name": "Maxime LUCE", "githubUsername": "SomaticIT"}, {"url": "https://github.com/EvanHahn", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}], "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/connect", "dist": {"shasum": "5ba7f3bc4fbbdeaff8dded952e5ff2cc53f8d858", "tarball": "https://registry.npmjs.org/@types/connect/-/connect-3.4.38.tgz", "fileCount": 5, "integrity": "sha512-K6uROf1LD88uDQqJCktA4yzL1YYAK6NgfsI0v/mTgyPKWsX1CnJ0XPSDhViejru1GcRkLWb8RlzFYJRqGUbaug==", "signatures": [{"sig": "MEUCIQDMQ4sLEO34WGyAb+bW9b7ptcEbIWgkUHea2fN3KwugPQIgDGQnNblrwP1B4oA3NRN8OSvcl0bcua3F7U+BvchS7Lo=", "keyid": "SHA256:jl3bwswu80PjjokCgh0o2w5c2U4LhQAE57gj9cz1kzA"}], "unpackedSize": 5907}, "main": "", "types": "index.d.ts", "scripts": {}, "_npmUser": {"name": "types", "email": "<EMAIL>"}, "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/connect"}, "description": "TypeScript definitions for connect", "directories": {}, "dependencies": {"@types/node": "*"}, "_hasShrinkwrap": false, "typeScriptVersion": "4.5", "_npmOperationalInternal": {"tmp": "tmp/connect_3.4.38_1699318654498_0.3916900081065784", "host": "s3://npm-registry-packages"}, "typesPublisherContentHash": "8990242237504bdec53088b79e314b94bec69286df9de56db31f22de403b4092"}}, "time": {"created": "2016-05-17T04:39:39.178Z", "modified": "2025-02-23T06:33:21.624Z", "3.4.16-alpha": "2016-05-17T04:39:39.178Z", "3.4.17-alpha": "2016-05-19T20:31:43.850Z", "3.4.22-alpha": "2016-05-20T19:21:16.694Z", "3.4.23-alpha": "2016-05-25T04:39:29.046Z", "3.4.24-alpha": "2016-07-01T19:05:36.202Z", "3.4.25-alpha": "2016-07-01T22:26:28.372Z", "3.4.26-alpha": "2016-07-02T02:09:46.423Z", "3.4.27-alpha": "2016-07-03T23:58:25.963Z", "3.4.28-alpha": "2016-07-08T19:51:20.347Z", "3.4.29": "2016-07-14T14:18:40.987Z", "3.4.30": "2016-09-19T16:34:08.396Z", "3.4.31": "2017-08-21T21:50:14.379Z", "3.4.32": "2018-04-17T01:02:05.518Z", "3.4.33": "2019-12-17T13:28:08.704Z", "3.4.34": "2020-12-08T20:45:34.173Z", "3.4.35": "2021-07-06T20:33:44.354Z", "3.4.36": "2023-09-04T16:13:32.146Z", "3.4.37": "2023-10-18T00:16:00.455Z", "3.4.38": "2023-11-07T00:57:34.681Z"}, "license": "MIT", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/connect", "repository": {"url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "type": "git", "directory": "types/connect"}, "description": "TypeScript definitions for connect", "contributors": [{"url": "https://github.com/SomaticIT", "name": "Maxime LUCE", "githubUsername": "SomaticIT"}, {"url": "https://github.com/EvanHahn", "name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>"}], "maintainers": [{"name": "types", "email": "<EMAIL>"}], "readme": "[object Object]", "readmeFilename": ""}