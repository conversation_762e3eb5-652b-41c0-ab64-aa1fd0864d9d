#!/usr/bin/env python3
"""
FINAL COMPREHENSIVE SYSTEM VALIDATION
Tests all critical components for maximum profit generation readiness
"""

import sys
import asyncio
import sqlite3
from pathlib import Path
from datetime import datetime

# Add current directory to path
sys.path.insert(0, str(Path(__file__).parent))

class FinalSystemValidator:
    def __init__(self):
        self.test_results = {
            'dependencies': {},
            'core_modules': {},
            'ai_components': {},
            'profit_systems': {},
            'database': {},
            'configuration': {}
        }
        self.critical_failures = []
        self.warnings = []
        
    def print_section(self, title):
        print(f"\n{'='*20} {title} {'='*20}")
    
    def test_dependencies(self):
        """Test critical package dependencies"""
        self.print_section("DEPENDENCY VALIDATION")
        
        critical_deps = {
            'pandas': 'Data processing framework',
            'numpy': 'Numerical computing',
            'sqlalchemy': 'Database ORM',
            'fastapi': 'Web API framework',
            'uvicorn': 'ASGI server',
            'yaml': 'Configuration parser',
            'requests': 'HTTP client',
            'aiohttp': 'Async HTTP',
            'websockets': 'WebSocket support',
            'psutil': 'System monitoring',
            'dotenv': 'Environment variables'
        }
        
        trading_deps = {
            'ccxt': 'Crypto exchange library',
            'pybit': 'Bybit API client',
            'ta': 'Technical analysis',
            'sklearn': 'Machine learning'
        }
        
        # Test critical dependencies
        for dep, desc in critical_deps.items():
            try:
                __import__(dep)
                print(f"✅ {dep:15} - {desc}")
                self.test_results['dependencies'][dep] = 'SUCCESS'
            except ImportError as e:
                print(f"❌ {dep:15} - MISSING: {desc}")
                self.test_results['dependencies'][dep] = f'FAILED: {e}'
                self.critical_failures.append(f"Missing critical dependency: {dep}")
        
        # Test trading dependencies
        print(f"\n📈 TRADING-SPECIFIC DEPENDENCIES:")
        for dep, desc in trading_deps.items():
            try:
                __import__(dep)
                print(f"✅ {dep:15} - {desc}")
                self.test_results['dependencies'][dep] = 'SUCCESS'
            except ImportError as e:
                print(f"⚠️ {dep:15} - MISSING: {desc}")
                self.test_results['dependencies'][dep] = f'FAILED: {e}'
                self.warnings.append(f"Missing trading dependency: {dep}")
    
    def test_core_modules(self):
        """Test core system modules"""
        self.print_section("CORE SYSTEM MODULES")
        
        core_modules = {
            'bybit_bot.core.config': 'Configuration management',
            'bybit_bot.core.logger': 'Logging system',
            'bybit_bot.database.connection': 'Database connectivity',
            'bybit_bot.core.bot_manager': 'Trading bot management'
        }
        
        for module, desc in core_modules.items():
            try:
                __import__(module)
                print(f"✅ {module:35} - {desc}")
                self.test_results['core_modules'][module] = 'SUCCESS'
            except ImportError as e:
                print(f"❌ {module:35} - FAILED: {str(e)[:40]}...")
                self.test_results['core_modules'][module] = f'FAILED: {e}'
                self.critical_failures.append(f"Core module failed: {module}")
    
    def test_ai_components(self):
        """Test SuperGPT and AI components"""
        self.print_section("SUPERGPT AI COMPONENTS")
        
        ai_modules = {
            'bybit_bot.ai.supergpt_integration': 'SuperGPT main integration',
            'bybit_bot.ai.meta_cognition_engine': 'Meta-cognition system',
            'bybit_bot.ai.memory_manager': 'Persistent memory',
            'bybit_bot.ai.self_correcting_code_evolution': 'Code evolution',
            'bybit_bot.ai.recursive_improvement_system': 'Recursive optimization'
        }
        
        for module, desc in ai_modules.items():
            try:
                __import__(module)
                print(f"✅ {module:45} - {desc}")
                self.test_results['ai_components'][module] = 'SUCCESS'
            except ImportError as e:
                print(f"⚠️ {module:45} - WARNING: {str(e)[:30]}...")
                self.test_results['ai_components'][module] = f'WARNING: {e}'
                self.warnings.append(f"AI component issue: {module}")
    
    def test_profit_systems(self):
        """Test profit maximization systems"""
        self.print_section("PROFIT MAXIMIZATION SYSTEMS")
        
        profit_modules = {
            'bybit_bot.profit_maximization.hyper_profit_engine': 'Hyper profit generation',
            'bybit_bot.profit_maximization.advanced_profit_engine': 'Advanced profit strategies'
        }
        
        for module, desc in profit_modules.items():
            try:
                __import__(module)
                print(f"✅ {module:50} - {desc}")
                self.test_results['profit_systems'][module] = 'SUCCESS'
            except ImportError as e:
                print(f"❌ {module:50} - FAILED: {str(e)[:30]}...")
                self.test_results['profit_systems'][module] = f'FAILED: {e}'
                self.critical_failures.append(f"Profit system failed: {module}")
    
    def test_database(self):
        """Test database connectivity and structure"""
        self.print_section("DATABASE VALIDATION")
        
        db_path = Path("E:/bybit_bot_data/bybit_trading_bot_production.db")
        
        if not db_path.exists():
            print(f"❌ Production database not found at {db_path}")
            self.critical_failures.append("Production database missing")
            return
        
        try:
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()
            
            # Check table count
            cursor.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='table'")
            table_count = cursor.fetchone()[0]
            print(f"✅ Database connected - {table_count} tables found")
            
            # Check critical tables
            critical_tables = [
                'trading_positions', 'trading_orders', 'account_balance',
                'performance_metrics', 'bot_config', 'news_sentiment',
                'system_logs'
            ]
            
            missing_tables = []
            for table in critical_tables:
                cursor.execute(f"SELECT COUNT(*) FROM sqlite_master WHERE type='table' AND name='{table}'")
                if cursor.fetchone()[0] == 0:
                    missing_tables.append(table)
                else:
                    cursor.execute(f"SELECT COUNT(*) FROM {table}")
                    count = cursor.fetchone()[0]
                    print(f"✅ Table {table}: {count} records")
            
            if missing_tables:
                print(f"❌ Missing tables: {missing_tables}")
                self.critical_failures.append(f"Missing database tables: {missing_tables}")
            
            # Test configuration
            cursor.execute("SELECT config_key, config_value FROM bot_config WHERE environment='PRODUCTION'")
            config_rows = cursor.fetchall()
            
            if config_rows:
                print(f"✅ Production configuration: {len(config_rows)} settings")
                for key, value in config_rows:
                    if 'trading_enabled' in key:
                        print(f"   🎯 {key}: {value}")
            else:
                print("⚠️ No production configuration found")
                self.warnings.append("Missing production configuration")
                
            conn.close()
            self.test_results['database']['connectivity'] = 'SUCCESS'
            
        except Exception as e:
            print(f"❌ Database error: {e}")
            self.critical_failures.append(f"Database connectivity failed: {e}")
            self.test_results['database']['connectivity'] = f'FAILED: {e}'
    
    def test_configuration(self):
        """Test system configuration"""
        self.print_section("CONFIGURATION VALIDATION")
        
        config_file = Path("config.yaml")
        if not config_file.exists():
            print("❌ config.yaml not found")
            self.critical_failures.append("Configuration file missing")
            return
        
        try:
            import yaml
            with open(config_file, 'r') as f:
                config = yaml.safe_load(f)
            
            # Check SuperGPT configuration
            supergpt = config.get('supergpt', {})
            if supergpt.get('enabled', False):
                print("✅ SuperGPT enabled for maximum profit")
                capabilities = supergpt.get('capabilities', {})
                enabled_caps = sum(1 for v in capabilities.values() if v)
                print(f"   🧠 Active capabilities: {enabled_caps}")
            else:
                print("❌ SuperGPT disabled - profit potential limited")
                self.critical_failures.append("SuperGPT not enabled")
            
            # Check system configuration  
            autonomous_enabled = False
            if hasattr(config, 'autonomous') and getattr(config.autonomous, 'enabled', False):
                autonomous_enabled = True
            elif config.get('autonomous', {}).get('enabled', False):
                autonomous_enabled = True
            elif config.get('system', {}).get('autonomous_mode', False):
                autonomous_enabled = True
                
            if autonomous_enabled:
                print("✅ Autonomous mode enabled")
            else:
                print("❌ Autonomous mode disabled")
                self.critical_failures.append("Autonomous mode not enabled")
            
            # Check database configuration
            database = config.get('database', {})
            db_url = database.get('url', '')
            if 'E:/bybit_bot_data' in db_url:
                print("✅ Database configured for production E-drive")
            else:
                print("⚠️ Database URL may not be optimized")
                self.warnings.append("Database configuration check needed")
            
            self.test_results['configuration']['yaml'] = 'SUCCESS'
            
        except Exception as e:
            print(f"❌ Configuration validation failed: {e}")
            self.critical_failures.append(f"Configuration error: {e}")
    
    def test_main_system(self):
        """Test main system integration"""
        self.print_section("MAIN SYSTEM INTEGRATION")
        
        try:
            import main_unified_system
            print("✅ main_unified_system imported successfully")
            
            if hasattr(main_unified_system, 'app'):
                print("✅ FastAPI app available")
            
            if hasattr(main_unified_system, 'UnifiedTradingSystem'):
                print("✅ UnifiedTradingSystem class available")
                
                # Test system initialization (without running)
                try:
                    system = main_unified_system.UnifiedTradingSystem()
                    print("✅ UnifiedTradingSystem can be instantiated")
                    
                    if hasattr(system, 'initialization_phases'):
                        phases = len(system.initialization_phases)
                        print(f"   📋 Initialization phases: {phases}")
                    
                except Exception as e:
                    print(f"⚠️ System instantiation warning: {str(e)[:50]}...")
                    self.warnings.append("System instantiation needs dependency fixes")
            
        except ImportError as e:
            print(f"❌ main_unified_system import failed: {e}")
            self.critical_failures.append("Main system import failed")
    
    def generate_final_report(self):
        """Generate final validation report"""
        self.print_section("FINAL VALIDATION REPORT")
        
        total_critical = len(self.critical_failures)
        total_warnings = len(self.warnings)
        
        print(f"📊 SYSTEM VALIDATION SUMMARY:")
        print(f"   🔥 Critical Failures: {total_critical}")
        print(f"   ⚠️ Warnings: {total_warnings}")
        
        if total_critical == 0:
            print(f"\n🎉 SYSTEM READY FOR MAXIMUM PROFIT GENERATION!")
            print(f"✅ All critical components operational")
            print(f"🚀 Ready for live trading deployment")
            
            if total_warnings > 0:
                print(f"\n⚠️ OPTIMIZATION RECOMMENDATIONS:")
                for warning in self.warnings:
                    print(f"   - {warning}")
                    
            return True
        else:
            print(f"\n❌ CRITICAL ISSUES MUST BE RESOLVED:")
            for failure in self.critical_failures:
                print(f"   🔥 {failure}")
            
            print(f"\n🔧 SYSTEM NOT READY FOR LIVE TRADING")
            return False
    
    async def run_full_validation(self):
        """Run complete system validation"""
        print("=" * 80)
        print("🚀 FINAL COMPREHENSIVE SYSTEM VALIDATION")
        print("🎯 OBJECTIVE: MAXIMUM PROFIT GENERATION READINESS")
        print("=" * 80)
        print(f"📅 Validation Time: {datetime.now()}")
        print(f"🐍 Python: {sys.executable}")
        
        # Run all validation tests
        self.test_dependencies()
        self.test_core_modules()
        self.test_ai_components()
        self.test_profit_systems()
        self.test_database()
        self.test_configuration()
        self.test_main_system()
        
        # Generate final report
        system_ready = self.generate_final_report()
        
        print(f"\n{'='*80}")
        if system_ready:
            print("🎯 VALIDATION RESULT: SYSTEM READY FOR MAXIMUM PROFIT GENERATION")
        else:
            print("🔧 VALIDATION RESULT: SYSTEM REQUIRES FIXES BEFORE DEPLOYMENT")
        print("=" * 80)
        
        return system_ready

async def main():
    validator = FinalSystemValidator()
    success = await validator.run_full_validation()
    return success

if __name__ == "__main__":
    result = asyncio.run(main())
    exit(0 if result else 1)
