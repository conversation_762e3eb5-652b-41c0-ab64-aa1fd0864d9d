@echo off
REM AUTONOMOUS TRADING SYSTEM - WINDOWS LAUNCHER
REM This script starts the trading system independently
REM Mobile app is optional - system runs autonomously

echo ================================================================
echo AUTONOMOUS TRADING SYSTEM - STARTING INDEPENDENT OPERATION
echo ================================================================
echo [LAUNCHER] System designed to run independently
echo [LAUNCHER] Mobile app provides optional control interface  
echo [LAUNCHER] Trading continues autonomously without mobile app
echo ================================================================

REM Change to script directory
cd /d "%~dp0"

REM Activate conda environment
echo [LAUNCHER] Activating conda environment...
call E:\conda\scripts\activate_bybit_trader.bat

REM Check if activation was successful
if %ERRORLEVEL% NEQ 0 (
    echo [ERROR] Failed to activate conda environment
    echo [INFO] Trying alternative activation method...
    call conda activate bybit-trader
)

REM Display environment info
echo [LAUNCHER] Using Python environment:
python --version
echo [LAUNCHER] Current directory: %CD%

REM Start the independent trading system
echo [LAUNCHER] Starting Independent Trading System...
echo [LAUNCHER] Press Ctrl+C to stop the system

REM Use independent launcher for autonomous operation
python independent_launcher.py

REM If that fails, fall back to main system
if %ERRORLEVEL% NEQ 0 (
    echo [LAUNCHER] Independent launcher failed, trying main system...
    python main_unified_system.py
)

echo [LAUNCHER] System stopped
pause
