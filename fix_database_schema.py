#!/usr/bin/env python3
"""
Database Schema Fix Script
Fixes critical database schema issues blocking profit generation
"""
import sys
import logging
import psycopg2
from pathlib import Path
import base64
import hashlib

# Add parent directory to path
sys.path.append(str(Path(__file__).parent))

from config_manager import ConfigManager, Environment

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(message)s')
logger = logging.getLogger(__name__)


def fix_database_schema():
    """Fix all database schema issues for maximum profit generation"""
    
    print("=" * 80)
    print("BYBIT TRADING BOT - DATABASE SCHEMA FIX")
    print("=" * 80)
    
    try:
        # Generate the same Fernet key as setup
        password = 'SuperGPT_AES256_Trading_Bot_2025_MAX_PROFIT_ENCRYPTION_KEY_SECURE'
        key = base64.urlsafe_b64encode(hashlib.sha256(password.encode()).digest())
        encryption_key = key.decode()
        
        # Initialize ConfigManager
        config_manager = ConfigManager(
            config_dir="config",
            environment=Environment.PRODUCTION,
            encryption_key=encryption_key
        )
        
        # Get database configuration
        db_config = config_manager.get_database_config()
        
        logger.info("[INFO] Connecting to PostgreSQL database...")
        
        # Connect to PostgreSQL
        conn = psycopg2.connect(
            host=db_config.host,
            port=db_config.port,
            database=db_config.database,
            user=db_config.user,
            password=db_config.password
        )
        conn.autocommit = True
        cursor = conn.cursor()
        
        logger.info("[OK] Connected to database successfully")
        
        # Fix 1: Add metadata column to positions table if missing
        logger.info("[INFO] Checking positions table schema...")
        
        cursor.execute("""
            SELECT EXISTS (
                SELECT FROM information_schema.columns 
                WHERE table_schema = 'public' 
                AND table_name = 'positions' 
                AND column_name = 'metadata'
            )
        """)
        
        if not cursor.fetchone()[0]:
            logger.info("[INFO] Adding metadata column to positions table...")
            cursor.execute("ALTER TABLE positions ADD COLUMN metadata JSONB DEFAULT '{}'::jsonb")
            logger.info("[OK] Metadata column added successfully!")
        else:
            logger.info("[OK] Metadata column already exists!")
        
        # Fix 2: Ensure all required tables exist
        logger.info("[INFO] Verifying all required tables exist...")
        
        # Check and create missing tables
        required_tables = {
            'positions': '''
                CREATE TABLE IF NOT EXISTS positions (
                    id SERIAL PRIMARY KEY,
                    symbol VARCHAR(20) NOT NULL,
                    side VARCHAR(10) NOT NULL,
                    size DECIMAL(20,8) NOT NULL,
                    entry_price DECIMAL(20,8),
                    current_price DECIMAL(20,8),
                    unrealized_pnl DECIMAL(20,8) DEFAULT 0,
                    realized_pnl DECIMAL(20,8) DEFAULT 0,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    status VARCHAR(20) DEFAULT 'active',
                    metadata JSONB DEFAULT '{}'::jsonb,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''',
            'trades': '''
                CREATE TABLE IF NOT EXISTS trades (
                    id SERIAL PRIMARY KEY,
                    position_id INTEGER REFERENCES positions(id) ON DELETE CASCADE,
                    symbol VARCHAR(20) NOT NULL,
                    side VARCHAR(10) NOT NULL,
                    quantity DECIMAL(20,8) NOT NULL,
                    price DECIMAL(20,8) NOT NULL,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    commission DECIMAL(20,8) DEFAULT 0,
                    realized_pnl DECIMAL(20,8) DEFAULT 0,
                    trade_type VARCHAR(20) DEFAULT 'market',
                    order_id VARCHAR(100),
                    metadata JSONB DEFAULT '{}'::jsonb,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''',
            'market_data': '''
                CREATE TABLE IF NOT EXISTS market_data (
                    id SERIAL PRIMARY KEY,
                    symbol VARCHAR(20) NOT NULL,
                    timestamp TIMESTAMP NOT NULL,
                    open_price DECIMAL(20,8),
                    high_price DECIMAL(20,8),
                    low_price DECIMAL(20,8),
                    close_price DECIMAL(20,8),
                    volume DECIMAL(20,8),
                    turnover DECIMAL(20,8),
                    interval_type VARCHAR(10),
                    metadata JSONB DEFAULT '{}'::jsonb,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            '''
        }
        
        for table_name, create_sql in required_tables.items():
            cursor.execute(create_sql)
            logger.info(f"[OK] Table {table_name} verified/created")
        
        # Fix 3: Create performance indexes
        logger.info("[INFO] Creating performance indexes...")
        
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_positions_symbol ON positions(symbol)",
            "CREATE INDEX IF NOT EXISTS idx_positions_timestamp ON positions(timestamp DESC)",
            "CREATE INDEX IF NOT EXISTS idx_trades_position_id ON trades(position_id)",
            "CREATE INDEX IF NOT EXISTS idx_trades_timestamp ON trades(timestamp DESC)",
            "CREATE INDEX IF NOT EXISTS idx_market_data_symbol_timestamp ON market_data(symbol, timestamp DESC)"
        ]
        
        for index_sql in indexes:
            cursor.execute(index_sql)
        
        logger.info("[OK] All performance indexes created")
        
        cursor.close()
        conn.close()
        
        print("\n" + "=" * 80)
        print("✅ SUCCESS: DATABASE SCHEMA FULLY FIXED")
        print("✅ ALL CRITICAL TABLES VERIFIED/CREATED")
        print("✅ METADATA COLUMN ADDED TO POSITIONS TABLE")
        print("✅ PERFORMANCE INDEXES CREATED")
        print("✅ SYSTEM READY FOR MAXIMUM PROFIT GENERATION")
        print("=" * 80)
        
        return True
        
    except Exception as e:
        logger.error(f"[ERROR] Database schema fix failed: {e}")
        print("\n" + "=" * 80)
        print("❌ FAILED: DATABASE SCHEMA FIX")
        print(f"❌ ERROR: {e}")
        print("=" * 80)
        return False


if __name__ == "__main__":
    fix_database_schema()
