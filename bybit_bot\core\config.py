"""
Enhanced Configuration management for the Autonomous Bybit Trading Bot
All features and SuperGPT functions enabled
"""

import os
import yaml
from pathlib import Path
from typing import Dict, Any, List, Optional
from dataclasses import dataclass, field
from dotenv import load_dotenv
import logging

# Load environment variables
load_dotenv()

logger = logging.getLogger(__name__)

@dataclass
class SystemConfig:
    """Core system configuration"""
    name: str = "Autonomous Bybit Trading Bot"
    version: str = "2.0.0"
    environment: str = "production"
    debug_mode: bool = True
    auto_start: bool = True
    self_healing: bool = True
    autonomous_mode: bool = True

@dataclass
class SuperGPTConfig:
    """SuperGPT functions configuration"""
    enabled: bool = True
    auto_learning: bool = True
    self_improvement: bool = True
    meta_cognition: bool = True
    advanced_reasoning: bool = True
    auto_optimization: bool = True
    max_profit_mode: bool = True
    self_evolution: bool = True
    recursive_improvement: bool = True
    autonomous_learning: bool = True
    profit_maximization: bool = True
    capabilities: Dict[str, bool] = field(default_factory=lambda: {
        "natural_language_processing": True,
        "code_generation": True,
        "strategy_optimization": True,
        "market_analysis": True,
        "risk_assessment": True,
        "performance_prediction": True,
        "anomaly_detection": True,
        "adaptive_learning": True
    })
    models: Dict[str, str] = field(default_factory=lambda: {
        "primary": "gpt-4-turbo",
        "fallback": "gpt-3.5-turbo"
    })
    learning: Dict[str, bool] = field(default_factory=lambda: {
        "continuous_learning": True,
        "experience_replay": True,
        "meta_learning": True,
        "transfer_learning": True,
        "reinforcement_learning": True
    })

@dataclass
class AutonomousConfig:
    """Autonomous operation configuration"""
    enabled: bool = True
    full_automation: bool = True
    self_healing: bool = True
    adaptive_strategies: bool = True
    continuous_learning: bool = True
    profit_optimization: bool = True
    risk_management: bool = True
    market_adaptation: bool = True

@dataclass
class EnhancedTradingConfig:
    """Enhanced trading configuration"""
    enabled: bool = True
    mode: str = "autonomous"
    environment: str = "production"
    paper_trading: bool = False
    live_trading: bool = True
    exchange: str = "bybit"
    max_risk_per_trade: float = 0.02
    max_daily_loss: float = 0.05
    max_position_size: float = 0.25
    leverage_range: List[int] = field(default_factory=lambda: [1, 5])
    stop_loss_percentage: float = 0.02
    take_profit_percentage: float = 0.04
    trailing_stop_percentage: float = 0.01
    position_sizing_method: str = "conservative"
    profit_compounding: bool = True
    dynamic_position_sizing: bool = True
    trading_pairs: List[str] = field(default_factory=lambda: ["BTCUSDT", "ETHUSDT"])
    order_timeout: int = 30
    slippage_tolerance: float = 0.005
    trading_cycle_interval: int = 30
    max_concurrent_orders: int = 5
    order_retry_attempts: int = 3
    position_update_interval: int = 10
    risk_management: Dict[str, Any] = field(default_factory=dict)
    portfolio: Dict[str, Any] = field(default_factory=dict)
    strategies: Dict[str, Any] = field(default_factory=dict)
    execution: Dict[str, Any] = field(default_factory=dict)
    symbols: List[str] = field(default_factory=list)
    profit_strategies: Dict[str, Any] = field(default_factory=dict)
    # Additional parameters from config.yaml
    max_positions: int = 10
    base_capital: float = 1000.0
    target_daily_return: float = 0.05
    reinvest_profits: bool = True
    compound_frequency: str = "hourly"
    pairs: List[str] = field(default_factory=lambda: ["BTCUSDT", "ETHUSDT"])
    markets: Dict[str, Any] = field(default_factory=dict)

    stop_loss_pct: float = 0.02
    take_profit_pct: float = 0.10
    max_daily_trades: int = 100
    
    def __init__(self, **kwargs):
        """Initialize EnhancedTradingConfig with flexible parameter handling"""
        # Set any provided parameters as attributes
        for key, value in kwargs.items():
            if hasattr(self, key) or not hasattr(self, key):
                setattr(self, key, value)
@dataclass
class EnhancedAPIKeysConfig:
    """Enhanced API keys configuration"""
    bybit: Dict[str, Any] = field(default_factory=dict)
    openai: Dict[str, str] = field(default_factory=dict)
    anthropic: Dict[str, str] = field(default_factory=dict)
    google: Dict[str, str] = field(default_factory=dict)
    newsapi: Dict[str, str] = field(default_factory=dict)
    alpha_vantage: Dict[str, str] = field(default_factory=dict)
    fred: Dict[str, str] = field(default_factory=dict)
    twitter: Dict[str, str] = field(default_factory=dict)
    reddit: Dict[str, str] = field(default_factory=dict)

    
    def __init__(self, **kwargs):
        """Initialize EnhancedAPIKeysConfig with flexible parameter handling"""
        # Initialize default values first
        self.bybit = {}
        self.openai = {}
        self.anthropic = {}
        self.google = {}
        self.newsapi = {}
        self.alpha_vantage = {}
        self.fred = {}
        self.twitter = {}
        self.reddit = {}
        
        # Set any provided parameters as attributes
        for key, value in kwargs.items():
            setattr(self, key, value)
@dataclass
class OpenRouterConfig:
    """OpenRouter API configuration"""
    enabled: bool = True
    api_key: str = ""
    base_url: str = "https://openrouter.ai/api/v1"
    site_url: str = "https://bybit-trading-bot.local"
    site_name: str = "Bybit Trading Bot"
    preferred_models: List[str] = field(default_factory=lambda: [
        "anthropic/claude-opus-4",
        "anthropic/claude-3.5-sonnet",
        "openai/gpt-4-turbo",
        "openai/gpt-4",
        "google/gemini-pro-1.5"
    ])
    rate_limit_delay: float = 1.0

@dataclass
class BybitConfig:
    """Bybit exchange configuration"""
    api_key: str = ""
    api_secret: str = ""
    testnet: bool = False
    base_url: str = "https://api.bybit.com"
    recv_window: int = 5000
    max_retries: int = 3
    rate_limit: int = 10
    retry_count: int = 3  # Add missing parameter
    
    def __init__(self, **kwargs):
        """Initialize with flexible parameter handling"""
        # Initialize default values first
        self.api_key = ""
        self.api_secret = ""
        self.testnet = False
        self.base_url = "https://api.bybit.com"
        self.recv_window = 5000
        self.max_retries = 3
        self.rate_limit = 10
        self.retry_count = 3
        
        # Set any provided parameters as attributes
        for key, value in kwargs.items():
            setattr(self, key, value)

@dataclass
class StrategiesConfig:
    """Trading strategies configuration"""
    scalping: Dict[str, Any] = field(default_factory=dict)
    grid_trading: Dict[str, Any] = field(default_factory=dict)
    arbitrage: Dict[str, Any] = field(default_factory=dict)
    momentum: Dict[str, Any] = field(default_factory=dict)
    funding_rate: Dict[str, Any] = field(default_factory=dict)
    mean_reversion: Dict[str, Any] = field(default_factory=dict)

@dataclass
class OptimizationConfig:
    """Performance optimization configuration"""
    high_frequency: bool = True
    latency_optimization: bool = True
    execution_speed: str = "maximum"
    slippage_tolerance: float = 0.001

@dataclass
class MonitoringConfig:
    """Monitoring configuration"""
    profit_alerts: bool = True
    performance_logging: bool = True
    real_time_pnl: bool = True
    profit_target_alerts: bool = True

@dataclass
class EnhancedDatabaseConfig:
    """Enhanced database configuration"""
    url: str = "sqlite:///./bybit_trading_bot.db"
    host: str = "localhost"
    port: int = 5432
    database: str = "bybit_trading_bot"
    user: str = "postgres"
    password: str = "password"
    pool_size: int = 20
    max_overflow: int = 50
    pool_timeout: int = 30
    pool_pre_ping: bool = True
    pool_recycle: int = 3600
    echo: bool = False
    ssl_mode: str = "prefer"
    connect_args: Dict[str, Any] = field(default_factory=dict)
    pragma: Dict[str, Any] = field(default_factory=dict)
    primary: Dict[str, Any] = field(default_factory=dict)
    redis: Dict[str, Any] = field(default_factory=dict)
    timeseries: Dict[str, Any] = field(default_factory=dict)

    
    def __init__(self, **kwargs):
        """Initialize EnhancedDatabaseConfig with flexible parameter handling"""
        # Set any provided parameters as attributes
        for key, value in kwargs.items():
            if hasattr(self, key) or not hasattr(self, key):
                setattr(self, key, value)
@dataclass
class EnhancedAIConfig:
    """Enhanced AI/ML configuration"""
    enabled: bool = True
    learning_enabled: bool = True
    self_healing_enabled: bool = True
    meta_learning_enabled: bool = True
    model_selection_enabled: bool = True
    autonomy_enabled: bool = True
    training: Dict[str, Any] = field(default_factory=dict)
    models: Dict[str, Any] = field(default_factory=dict)
    features: Dict[str, bool] = field(default_factory=dict)
    prediction: Dict[str, Any] = field(default_factory=dict)

# ...existing dataclasses...

    
    def __init__(self, **kwargs):
        """Initialize EnhancedAIConfig with flexible parameter handling"""
        # Set any provided parameters as attributes
        for key, value in kwargs.items():
            if hasattr(self, key) or not hasattr(self, key):
                setattr(self, key, value)
class EnhancedBotConfig:
    """Enhanced configuration class with all features enabled"""
    
    def __init__(self, config_file: Optional[str] = None):
        self.config_file = config_file or "config.yaml"
        self.config_path = Path(self.config_file)
        
        # Initialize all components
        self._load_enhanced_config()
        self._setup_logging()
        self._validate_enhanced_config()
        
    def _load_enhanced_config(self):
        """Load enhanced configuration with all features"""
        if self.config_path.exists():
            with open(self.config_path, 'r') as f:
                config_data = yaml.safe_load(f)
        else:
            # Create default enhanced config
            config_data = self._get_enhanced_default_config()
            self._save_config_to_file(config_data)
        
        # Override with environment variables
        self._override_with_env_vars(config_data)
        
        # Parse enhanced configuration
        self._parse_enhanced_config(config_data)
    
    def _parse_enhanced_config(self, config_data: Dict[str, Any]):
        """Parse enhanced configuration data"""
        # Core components
        self.system = SystemConfig(**config_data.get("system", {}))
        self.supergpt = SuperGPTConfig(**config_data.get("supergpt", {}))
        self.autonomous = AutonomousConfig(**config_data.get("autonomous", {}))
        self.trading = EnhancedTradingConfig(**config_data.get("trading", {}))
        self.api_keys = EnhancedAPIKeysConfig(**config_data.get("api_keys", {}))
        self.database = EnhancedDatabaseConfig(**config_data.get("database", {}))
        self.ai = EnhancedAIConfig(**config_data.get("ai", {}))
        
        # New configuration sections
        self.openrouter = OpenRouterConfig(**config_data.get("openrouter", {}))
        self.bybit = BybitConfig(**config_data.get("bybit", {}))
        self.strategies = StrategiesConfig(**config_data.get("strategies", {}))
        self.optimization = OptimizationConfig(**config_data.get("optimization", {}))
        self.monitoring = MonitoringConfig(**config_data.get("monitoring", {}))
        
        # Additional components
        self.data_crawler = config_data.get("data_crawler", {})
        self.performance = config_data.get("performance", {})
        self.hardware = config_data.get("hardware", {})
        self.logging_config = config_data.get("logging", {})
        self.api = config_data.get("api", {})
        self.security = config_data.get("security", {})
        self.notifications = config_data.get("notifications", {})
        self.backtesting = config_data.get("backtesting", {})
        self.development = config_data.get("development", {})
        self.integrations = config_data.get("integrations", {})
        self.experimental = config_data.get("experimental", {})
        self.risk_management = config_data.get("risk_management", {})
        
        # Convenience properties
        self._debug_mode = self.system.debug_mode
        
        # Add missing attributes for compatibility
        self.max_risk_percentage = config_data.get("max_risk_percentage", 2.0)
        if hasattr(self.trading, 'max_risk_percentage'):
            self.max_risk_percentage = self.trading.max_risk_percentage
        elif hasattr(self.trading, 'max_risk_per_trade'):
            self.max_risk_percentage = self.trading.max_risk_per_trade * 100
        
        # Add max_drawdown_percentage for risk manager compatibility
        self.max_drawdown_percentage = config_data.get("max_drawdown_percentage", 5.0)
        if hasattr(self.trading, 'max_drawdown_percentage'):
            self.max_drawdown_percentage = self.trading.max_drawdown_percentage
        elif hasattr(self.risk_management, 'max_drawdown'):
            self.max_drawdown_percentage = self.risk_management.get('max_drawdown', 5.0)
        
        # Add max_open_positions for risk manager compatibility
        self.max_open_positions = config_data.get("max_open_positions", 5)
        if hasattr(self.trading, 'max_open_positions'):
            self.max_open_positions = self.trading.max_open_positions
        elif hasattr(self.risk_management, 'max_positions'):
            self.max_open_positions = self.risk_management.get('max_positions', 5)
        
        self.autonomous_mode = self.system.autonomous_mode
        self.supergpt_enabled = self.supergpt.enabled
        self.trading_enabled = self.trading.enabled
    
    def _get_enhanced_default_config(self) -> Dict[str, Any]:
        """Get enhanced default configuration with all features enabled"""
        return {
            "system": {
                "name": "Autonomous Bybit Trading Bot",
                "version": "2.0.0",
                "environment": "production",
                "debug_mode": True,
                "auto_start": True,
                "self_healing": True,
                "autonomous_mode": True
            },
            "supergpt": {
                "enabled": True,
                "auto_learning": True,
                "self_improvement": True,
                "meta_cognition": True,
                "advanced_reasoning": True,
                "capabilities": {
                    "natural_language_processing": True,
                    "code_generation": True,
                    "strategy_optimization": True,
                    "market_analysis": True,
                    "risk_assessment": True,
                    "performance_prediction": True,
                    "anomaly_detection": True,
                    "adaptive_learning": True
                },
                "models": {
                    "primary": "gpt-4-turbo",
                    "fallback": "gpt-3.5-turbo"
                },
                "learning": {
                    "continuous_learning": True,
                    "experience_replay": True,
                    "meta_learning": True,
                    "transfer_learning": True,
                    "reinforcement_learning": True
                }
            },
            # ...rest of enhanced default config...
        }
    
    def _setup_logging(self):
        """Setup enhanced logging"""
        log_config = self.logging_config
        logging.basicConfig(
            level=getattr(logging, log_config.get("level", "INFO")),
            format=log_config.get("format", "%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        )
        
    def _validate_enhanced_config(self) -> List[str]:
        """Validate enhanced configuration"""
        errors = []
        
        # Validate SuperGPT configuration
        if self.supergpt.enabled and not self.api_keys.openai.get("api_key"):
            errors.append("OpenAI API key required for SuperGPT functions")
        
        # Validate trading configuration
        if self.trading.enabled and not self.api_keys.bybit.get("api_key"):
            errors.append("Bybit API credentials required for trading")
        
        # Validate AI configuration
        if self.ai.enabled and not any([
            self.api_keys.openai.get("api_key"),
            self.api_keys.anthropic.get("api_key"),
            self.api_keys.google.get("api_key")
        ]):
            errors.append("At least one AI API key required for AI functions")
        
        if errors:
            logger.warning(f"Configuration validation errors: {errors}")
        
        return errors
    
    def _override_with_env_vars(self, config_data: Dict[str, Any]):
        """Override configuration with environment variables"""
        # Bybit API credentials
        if os.getenv("BYBIT_API_KEY"):
            config_data.setdefault("bybit", {})["api_key"] = os.getenv("BYBIT_API_KEY")
        if os.getenv("BYBIT_API_SECRET"):
            config_data.setdefault("bybit", {})["api_secret"] = os.getenv("BYBIT_API_SECRET")
        if os.getenv("BYBIT_TESTNET"):
            config_data.setdefault("bybit", {})["testnet"] = os.getenv("BYBIT_TESTNET", "false").lower() == "true"
        
        # Trading configuration
        if os.getenv("LIVE_TRADING"):
            config_data.setdefault("trading", {})["live_trading"] = os.getenv("LIVE_TRADING", "true").lower() == "true"
        if os.getenv("PAPER_TRADING"):
            config_data.setdefault("trading", {})["paper_trading"] = os.getenv("PAPER_TRADING", "false").lower() == "true"
        if os.getenv("MAX_POSITION_SIZE"):
            config_data.setdefault("trading", {})["max_position_size"] = float(os.getenv("MAX_POSITION_SIZE", "1000"))
        
        # SuperGPT API keys
        if os.getenv("OPENAI_API_KEY"):
            config_data.setdefault("api_keys", {}).setdefault("openai", {})["api_key"] = os.getenv("OPENAI_API_KEY")
        if os.getenv("ANTHROPIC_API_KEY"):
            config_data.setdefault("api_keys", {}).setdefault("anthropic", {})["api_key"] = os.getenv("ANTHROPIC_API_KEY")
        if os.getenv("GOOGLE_API_KEY"):
            config_data.setdefault("api_keys", {}).setdefault("google", {})["api_key"] = os.getenv("GOOGLE_API_KEY")
        
        # Process ${VAR} substitutions in YAML
        self._substitute_env_vars(config_data)
    
    def _substitute_env_vars(self, obj: Any) -> Any:
        """Recursively substitute environment variables in config data"""
        if isinstance(obj, dict):
            return {key: self._substitute_env_vars(value) for key, value in obj.items()}
        elif isinstance(obj, list):
            return [self._substitute_env_vars(item) for item in obj]
        elif isinstance(obj, str) and obj.startswith("${") and obj.endswith("}"):
            env_var = obj[2:-1]
            return os.getenv(env_var, obj)
        else:
            return obj
    
    def _save_config_to_file(self, config_data: Dict[str, Any]):
        """Save configuration to file"""
        with open(self.config_path, 'w') as f:
            yaml.dump(config_data, f, default_flow_style=False, indent=2)
    
    def is_feature_enabled(self, feature_path: str) -> bool:
        """Check if a feature is enabled using dot notation"""
        parts = feature_path.split('.')
        current = self
        
        try:
            for part in parts:
                if hasattr(current, part):
                    current = getattr(current, part)
                elif isinstance(current, dict):
                    current = current.get(part, False)
                else:
                    return False
            return bool(current)
        except:
            return False
    
    def get_feature_config(self, feature_path: str) -> Any:
        """Get configuration for a feature using dot notation"""
        parts = feature_path.split('.')
        current = self
        
        try:
            for part in parts:
                if hasattr(current, part):
                    current = getattr(current, part)
                elif isinstance(current, dict):
                    current = current.get(part, {})
                else:
                    return {}
            return current
        except:
            return {}
    
    def enable_all_features(self):
        """Enable all available features"""
        self.supergpt.enabled = True
        self.trading.enabled = True
        self.ai.enabled = True
        self.ai.learning_enabled = True
        self.ai.self_healing_enabled = True
        self.ai.meta_learning_enabled = True
        self.ai.model_selection_enabled = True
        self.ai.autonomy_enabled = True
        
        # Enable all strategies
        for strategy in self.trading.strategies:
            if isinstance(self.trading.strategies[strategy], dict):
                self.trading.strategies[strategy]["enabled"] = True
        
        logger.info("All features enabled")

    def get_api_key(self, service: str) -> Dict[str, Any]:
        """Get API key configuration for a service"""
        return getattr(self.api_keys, service, {})

    def get_trading_pairs(self) -> List[str]:
        """Get list of trading pairs"""
        return getattr(self.trading, 'trading_pairs', ['BTCUSDT', 'ETHUSDT'])

    @property
    def api_host(self) -> str:
        """Get API host - Allow mobile access"""
        return "0.0.0.0"  # Listen on all interfaces for mobile access

    @property
    def api_port(self) -> int:
        """Get API port"""
        return 8000

    @property
    def debug_mode(self) -> bool:
        """Get debug mode"""
        return getattr(self, '_debug_mode', True)

    @property
    def paper_trading(self) -> bool:
        """Get paper trading mode"""
        return getattr(self.trading, 'paper_trading', True)

    def save_config(self):
        """Save current configuration to file"""
        config_data = {
            "trading": self.trading.__dict__,
            "api_keys": self.api_keys.__dict__,
            "database": self.database.__dict__,
            "backtesting": self.backtesting.__dict__,
            "development": self.development.__dict__
        }

        with open(self.config_path, 'w') as f:
            yaml.dump(config_data, f, default_flow_style=False)

    def validate_config(self) -> List[str]:
        """Validate configuration and return list of errors"""
        errors = []
        
        # Check required API keys
        if not self.api_keys.bybit.get("api_key") or not self.api_keys.bybit.get("api_secret"):
            errors.append("Bybit API credentials are required")
        
        # Check trading pairs
        if not self.trading.trading_pairs:
            errors.append("At least one trading pair must be configured")
        
        # Check strategies
        enabled_strategies = [name for name, config in self.trading.strategies.items() 
                            if config.get("enabled", False)]
        if not enabled_strategies:
            errors.append("At least one trading strategy must be enabled")
        
        # Check database configuration
        if not hasattr(self.database, 'primary') or not self.database.primary:
            errors.append("Database configuration must be properly configured")

        return errors


# Create global config instance
config = None

# Alias for backward compatibility
BotConfig = EnhancedBotConfig

def get_config(config_file: Optional[str] = None) -> EnhancedBotConfig:
    """Get global configuration instance"""
    global config
    if config is None:
        config = EnhancedBotConfig(config_file)
    return config

def reload_config(config_file: Optional[str] = None):
    """Reload configuration"""
    global config
    config = EnhancedBotConfig(config_file)
    return config
