#!/usr/bin/env python3
"""
MAXIMUM PROFIT TRADING STARTUP
Starts the trading bot with aggressive profit-focused settings
"""

import asyncio
import os
from datetime import datetime

print("=" * 60)
print("STARTING MAXIMUM PROFIT TRADING BOT")
print("=" * 60)
print(f"Timestamp: {datetime.now()}")
print("Mode: AGGRESSIVE PROFIT GENERATION")
print("=" * 60)

# Force aggressive settings
os.environ["AGGRESSIVE_TRADING"] = "true"
os.environ["HIGH_FREQUENCY_TRADING"] = "true"
os.environ["MAXIMUM_PROFIT_MODE"] = "true"

async def start_trading_bot():
    """Start the trading bot with profit focus"""
    try:
        # Import the main system
        from main_unified_system import UnifiedTradingSystem
        
        print("[STARTING] Initializing trading system...")
        system = UnifiedTradingSystem()
        
        print("[STARTING] Configuring for maximum profits...")
        # Configure for aggressive trading
        if hasattr(system, 'bot_manager') and system.bot_manager:
            # Set aggressive parameters
            system.bot_manager.trading_symbols = [
                "BTCUSDT", "ETHUSDT", "SOLUSDT", "ADAUSDT", "DOTUSDT",
                "BNBUSDT", "XRPUSDT", "MATICUSDT", "LINKUSDT", "AVAXUSDT"
            ]
            
            # Increase trading frequency
            if hasattr(system.bot_manager, 'config'):
                system.bot_manager.config.trading_cycle_interval = 5  # 5 second cycles
        
        print("[STARTING] Starting profit generation...")
        await system.start()
        
        print("TRADING BOT STARTED - GENERATING PROFITS!")
        print("Monitor profits in real-time...")
        
        # Keep running
        try:
            while True:
                await asyncio.sleep(60)
                # Print status every minute
                if hasattr(system, 'bot_manager') and system.bot_manager:
                    status = await system.bot_manager.get_status()
                    pnl = status.get('trading_metrics', {}).get('total_pnl', 0)
                    positions = status.get('trading_metrics', {}).get('active_positions_count', 0)
                    print(f"Current P&L: ${pnl:.2f} | Positions: {positions}")
        except KeyboardInterrupt:
            print("Stopping trading bot...")
            await system.shutdown()
    
    except Exception as e:
        print(f"ERROR starting trading bot: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    try:
        asyncio.run(start_trading_bot())
    except KeyboardInterrupt:
        print("Trading bot stopped by user")
    except Exception as e:
        print(f"CRITICAL ERROR: {e}")
